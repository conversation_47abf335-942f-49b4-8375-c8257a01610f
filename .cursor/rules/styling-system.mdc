---
description: 
globs: 
alwaysApply: false
---
# 样式系统指南

## CSS变量与Less变量使用原则

### 使用场景划分
- **CSS变量**: 用于底层设计token定义，支持动态主题切换
- **Less变量**: 用于组件样式计算和复杂样式逻辑

### CSS变量命名规范
```less
// 颜色系统
--eui-primary-pri-500     // 主色
--eui-function-danger-500 // 功能色
--eui-text-title          // 文本色
--eui-bg-body            // 背景色
--eui-fill-hover         // 填充色

// 尺寸系统
--eui-radius-s           // 圆角
--eui-font-head-r1       // 字体

// 边框系统
--eui-line-border-component // 边框色
```

## 样式文件结构
参考 [Button样式文件](mdc:src/components/button/style/index.less)：

### 1. 变量定义
```less
@class-prefix-button: ~'eui-button';

.@{class-prefix-button} {
  --color: var(--eui-bg-body);
  --text-color: var(--eui-text-title);
  --border-radius: var(--eui-radius-s);
}
```

### 2. 基础样式
```less
.@{class-prefix-button} {
  display: inline-block;
  border: var(--border-width) var(--border-style) var(--border-color);
  background: var(--background-color);
  color: var(--text-color);
}
```

### 3. 状态样式
```less
&:hover {
  --color: var(--eui-primary-pri-400);
}

&:disabled {
  --color: var(--eui-fill-disabled);
}
```

## 主题系统
- **默认主题**: 基础设计系统颜色
- **高亮主题**: 强调色主题
- **危险主题**: 警告/错误主题

## 响应式设计
- 使用CSS变量实现响应式断点
- 支持移动端适配
- 遵循无障碍设计原则

## 样式优化
- 避免深层嵌套选择器
- 使用CSS变量减少重复代码
- 保持样式的可维护性和扩展性

## 全局样式
全局样式定义在 [src/global/](mdc:src/global) 目录中，包括：
- 基础重置样式
- 设计token定义
- 通用工具类
