---
description: 
globs: 
alwaysApply: false
---
# 故障排除指南

## 常见开发问题

### 1. 依赖安装问题
**问题**: pnpm install 失败
**解决方案**:
```bash
# 清理缓存
pnpm store prune
rm -rf node_modules
rm pnpm-lock.yaml

# 重新安装
pnpm install
```

### 2. TypeScript类型错误
**问题**: 组件类型定义不正确
**解决方案**:
- 检查 [tsconfig.json](mdc:tsconfig.json) 路径映射
- 确保导入路径正确
- 使用 `@hose/eui` 而不是相对路径

### 3. 样式不生效
**问题**: 组件样式没有正确加载
**解决方案**:
- 确保样式文件在组件中正确导入
- 检查CSS变量定义是否正确
- 验证Less编译是否成功

### 4. 构建失败
**问题**: gulp build 报错
**解决方案**:
```bash
# 检查构建配置
cat gulpfile.js

# 清理构建目录
rm -rf lib

# 重新构建
pnpm build
```

## 测试相关问题

### 1. 测试用例失败
**问题**: Jest测试不通过
**解决方案**:
- 检查 [jest.config.js](mdc:jest.config.js) 配置
- 确保测试环境正确设置
- 验证模拟数据和断言

### 2. 覆盖率不足
**问题**: 测试覆盖率低于要求
**解决方案**:
- 添加缺失的测试用例
- 测试边界条件和错误情况
- 使用 `pnpm test-with-coverage` 查看详细报告

## 样式系统问题

### 1. CSS变量未定义
**问题**: 样式中使用了未定义的CSS变量
**解决方案**:
- 检查 [全局样式](mdc:src/global) 中的变量定义
- 确保变量命名符合规范
- 验证变量作用域

### 2. 主题切换失效
**问题**: 动态主题切换不工作
**解决方案**:
- 确保使用CSS变量而非Less变量
- 检查变量继承关系
- 验证JavaScript主题切换逻辑

## 组件开发问题

### 1. 组件不显示
**问题**: 新开发的组件无法渲染
**解决方案**:
- 检查组件是否正确导出
- 验证 [src/index.ts](mdc:src/index.ts) 中的导出
- 确保组件props类型正确

### 2. 事件处理异常
**问题**: 组件事件回调不触发
**解决方案**:
- 检查事件绑定是否正确
- 验证事件冒泡和阻止默认行为
- 确保异步处理逻辑正确

## 构建和发布问题

### 1. 版本发布失败
**问题**: npm publish 报错
**解决方案**:
```bash
# 检查版本号
npm version

# 验证构建产物
ls -la lib/

# 检查发布配置
cat package.json
```

### 2. CDN上传失败
**问题**: 静态资源上传CDN失败
**解决方案**:
- 检查CDN配置和权限
- 验证文件路径和命名
- 确认网络连接正常

## 性能优化

### 1. 构建速度慢
**解决方案**:
- 使用增量构建
- 优化Webpack配置
- 启用并行处理

### 2. 包体积过大
**解决方案**:
- 分析bundle大小
- 移除未使用的依赖
- 启用tree-shaking

## 调试技巧

### 1. 开发环境调试
```bash
# 启动开发服务器
pnpm start

# 查看详细日志
DEBUG=* pnpm start
```

### 2. 生产环境调试
```bash
# 构建并分析
pnpm build
npx webpack-bundle-analyzer lib/bundle/stats.json
```

## 获取帮助

如果以上解决方案无法解决问题，请：
1. 查看项目文档和README
2. 检查相关issue和PR
3. 联系项目维护者
4. 提交详细的bug报告
