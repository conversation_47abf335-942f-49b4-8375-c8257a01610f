---
description: 
globs: 
alwaysApply: false
---
# 组件目录

## 基础组件

### 布局组件
- **[Grid](mdc:src/components/grid)** - 栅格布局系统
- **[Layout](mdc:src/components/layout)** - 页面布局容器
- **[Space](mdc:src/components/space)** - 间距组件
- **[Divider](mdc:src/components/divider)** - 分割线

### 导航组件
- **[Breadcrumb](mdc:src/components/breadcrumb)** - 面包屑导航
- **[Menu](mdc:src/components/menu)** - 导航菜单
- **[Pagination](mdc:src/components/pagination)** - 分页器
- **[Steps](mdc:src/components/steps)** - 步骤条
- **[Anchor](mdc:src/components/anchor)** - 锚点定位

### 数据录入
- **[Button](mdc:src/components/button)** - 按钮
- **[Input](mdc:src/components/input)** - 输入框
- **[InputNumber](mdc:src/components/input-number)** - 数字输入框
- **[Select](mdc:src/components/select)** - 选择器
- **[Checkbox](mdc:src/components/checkbox)** - 多选框
- **[Radio](mdc:src/components/radio)** - 单选框
- **[Switch](mdc:src/components/switch)** - 开关
- **[Slider](mdc:src/components/slider)** - 滑动输入条
- **[Rate](mdc:src/components/rate)** - 评分
- **[Upload](mdc:src/components/upload)** - 文件上传
- **[Form](mdc:src/components/form)** - 表单

### 数据展示
- **[Table](mdc:src/components/table)** - 表格
- **[List](mdc:src/components/list)** - 列表
- **[Card](mdc:src/components/card)** - 卡片
- **[Descriptions](mdc:src/components/descriptions)** - 描述列表
- **[Badge](mdc:src/components/badge)** - 徽标数
- **[Tag](mdc:src/components/tag)** - 标签
- **[Avatar](mdc:src/components/avatar)** - 头像
- **[Progress](mdc:src/components/progress)** - 进度条
- **[Timeline](mdc:src/components/timeline)** - 时间轴
- **[Tree](mdc:src/components/tree)** - 树形控件
- **[Image](mdc:src/components/image)** - 图片
- **[Carousel](mdc:src/components/carousel)** - 走马灯

### 反馈组件
- **[Alert](mdc:src/components/alert)** - 警告提示
- **[Message](mdc:src/components/message)** - 全局提示
- **[Notification](mdc:src/components/notification)** - 通知提醒框
- **[Modal](mdc:src/components/modal)** - 对话框
- **[Drawer](mdc:src/components/drawer)** - 抽屉
- **[Popconfirm](mdc:src/components/popconfirm)** - 气泡确认框
- **[Spin](mdc:src/components/spin)** - 加载中
- **[Skeleton](mdc:src/components/skeleton)** - 骨架屏

### 其他组件
- **[Tooltip](mdc:src/components/tooltip)** - 文字提示
- **[Popover](mdc:src/components/popover)** - 气泡卡片
- **[Dropdown](mdc:src/components/dropdown)** - 下拉菜单
- **[Tabs](mdc:src/components/tabs)** - 标签页
- **[Collapse](mdc:src/components/collapse)** - 折叠面板
- **[Cascader](mdc:src/components/cascader)** - 级联选择
- **[TreeSelect](mdc:src/components/tree-select)** - 树选择
- **[Transfer](mdc:src/components/transfer)** - 穿梭框
- **[DatePicker](mdc:src/components/date-picker)** - 日期选择器
- **[TimePicker](mdc:src/components/time-picker)** - 时间选择器
- **[Segmented](mdc:src/components/segmented)** - 分段控制器

## 特殊组件

### 业务组件
- **[PageHeader](mdc:src/components/page-header)** - 页头
- **[ErrorBlock](mdc:src/components/error-block)** - 异常页面
- **[Ellipsis](mdc:src/components/ellipsis)** - 文本省略
- **[Questionnaire](mdc:src/components/questionnaire)** - 问卷组件

### 配置组件
- **[ConfigProvider](mdc:src/components/config-provider)** - 全局配置

## 组件使用
所有组件都可以从主入口文件导入：
```typescript
import { Button, Input, Table } from '@hose/eui';
```

详细的组件API和使用示例请参考各组件目录下的文档和demo。
