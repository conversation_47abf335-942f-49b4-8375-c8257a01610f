---
description: 
globs: 
alwaysApply: false
---
# 组件开发指南

## 组件结构规范
每个组件都应遵循以下目录结构：
```
src/components/[component-name]/
├── index.tsx          # 组件主文件
├── index.md           # 组件文档和demo引入 (必须)
├── style/
│   └── index.less     # 组件样式
├── demo/              # 组件演示
└── __tests__/         # 单元测试
```

**注意**: README.md文件是可选的，大多数组件都不需要创建README.md，因为index.md已经包含了完整的文档。

## Demo开发规范

### 1. Demo文件结构
每个组件的demo目录应包含：
- `basic.tsx` - 基础用法演示
- `advanced.tsx` - 高级功能演示
- 其他特定场景demo文件

### 2. DemoBlock使用规范
所有demo必须使用 `DemoBlock` 组件包装，参考 [Button Demo](mdc:src/components/button/demo/demo1.tsx)：

```typescript
import React from 'react';
import { DemoBlock } from 'demos';
import { ComponentName, Space } from '@hose/eui';

export default () => {
  return (
    <>
      <DemoBlock title="基础用法">
        <Space size={16} align="center">
          <ComponentName />
        </Space>
      </DemoBlock>
      
      <DemoBlock title="不同尺寸">
        <Space size={16} align="center">
          <ComponentName size="small" />
          <ComponentName size="large" />
        </Space>
      </DemoBlock>
    </>
  );
};
```

### 3. Demo编写要点
- **导入规范**: 从 `demos` 导入 `DemoBlock`，从 `@hose/eui` 导入EUI组件
- **布局组件**: 使用 `Space` 组件进行布局，避免内联样式
- **标题规范**: DemoBlock的title应简洁明了，描述功能特点
- **代码结构**: 使用React Fragment (`<>`) 包装多个DemoBlock
- **演示数据**: 使用有意义的演示数据，避免无意义的占位符

### 4. 常用布局模式
```typescript
// 水平排列
<Space size={16} align="center">
  {/* 组件列表 */}
</Space>

// 垂直排列
<Space direction="vertical" size={12}>
  {/* 组件列表 */}
</Space>

// 换行排列
<Space size={16} wrap>
  {/* 组件列表 */}
</Space>
```

## 新增组件配置清单
当新增组件时，需要完成以下配置：

### 1. 更新配置文件
- **config/components.ts**: 将组件路径添加到对应分类中
  ```typescript
  feedback: [
    // ... 其他组件
    '/components/attachment',  // 新增组件
  ]
  ```
- **src/index.ts**: 导出组件和类型定义
  ```typescript
  export { default as Attachment, AttachmentList } from './components/attachment';
  export type { AttachmentProps, AttachmentListProps, AttachmentFile } from './components/attachment';
  ```

### 2. 组件分类说明
- **basic**: 基础组件 (Button, Icon, Space, Divider)
- **dataDisplay**: 数据展示 (Table, List, Card, Tag等)
- **dataEntry**: 数据录入 (Form, Input, Select, Upload等)
- **feedback**: 反馈 (Alert, Message, Modal, Attachment等)
- **navigationAndLayout**: 导航布局 (Menu, Breadcrumb, Tabs等)
- **other**: 其他 (ConfigProvider, Anchor)
- **business**: 业务组件 (Questionnaire)
- **experimental**: 试验性组件

## 组件开发模式
参考 [Button组件](mdc:src/components/button/index.tsx) 作为标准模板：

### 1. 类型定义
```typescript
export type ComponentProps = {
  // 组件属性定义
} & NativeProps<never>;

export type ComponentRef = HTMLElement;
```

### 2. 组件实现
- 使用 `forwardRef` 包装组件
- 使用 `mergeProps` 合并默认属性
- 使用 `withNativeProps` 处理原生属性
- 添加 `__EUI_COMPONENT` 标识

### 3. 样式规范
参考 [Button样式](mdc:src/components/button/style/index.less)：
- 使用CSS变量和Less变量结合
- 遵循BEM命名规范
- 支持主题切换

## 命名规范
- **组件名**: PascalCase (如 `Button`, `DatePicker`)
- **文件名**: kebab-case (如 `button`, `date-picker`)
- **CSS类名**: `eui-[component-name]` (如 `eui-button`)
- **CSS变量**: `--eui-[category]-[name]` (如 `--eui-primary-pri-500`)

## 导出规范
所有组件必须在 [src/index.ts](mdc:src/index.ts) 中导出：
```typescript
export { default as ComponentName } from './components/component-name';
export type { ComponentProps } from './components/component-name';
```

## 测试要求
- 每个组件都需要单元测试
- 测试文件放在 `__tests__` 目录
- 使用 `@testing-library/react` 进行测试
- 确保测试覆盖率

## 文档要求
- 每个组件需要完整的API文档
- 提供使用示例和演示
- 说明组件的设计理念和使用场景

## 文档开发规范

### 1. index.md文件
每个组件必须创建`index.md`文件来引入demo和提供API文档，参考 [Button文档](mdc:src/components/button/index.md)：

```markdown
# ComponentName 组件名 ✅

组件描述。

## 何时使用

- 使用场景1
- 使用场景2

## 代码演示

<code src="./demo/basic.tsx"></code>
<code src="./demo/advanced.tsx"></code>

## API

### ComponentName

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| prop1 | 属性说明 | `string` | - |
```

### 2. demo引入规范
- 使用 `<code src="./demo/filename.tsx"></code>` 引入demo文件
- demo文件按功能分类：basic.tsx（基础）、advanced.tsx（高级）、table.tsx（表格场景）等
- 每个demo文件对应一个演示场景
