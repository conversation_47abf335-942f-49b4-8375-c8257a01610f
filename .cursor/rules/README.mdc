---
description: 
globs: 
alwaysApply: false
---
# EUI组件库 Cursor 规则

欢迎使用EUI组件库！这些规则将帮助您更好地理解和开发这个基于React的UI组件库。

## 📚 规则索引

### 🏗️ [项目概览](mdc:.cursor/rules/project-overview.mdc)
了解EUI组件库的基本信息、技术栈和项目结构

### 🔧 [组件开发指南](mdc:.cursor/rules/component-development.mdc)
学习如何开发、维护和扩展组件的完整指南

### 🎨 [样式系统指南](mdc:.cursor/rules/styling-system.mdc)
掌握CSS变量、Less变量和主题系统的使用方法

### ⚡ [开发工作流指南](mdc:.cursor/rules/development-workflow.mdc)
了解开发环境设置、构建流程和发布流程

### 📦 [组件目录](mdc:.cursor/rules/component-catalog.mdc)
查看所有可用组件的完整列表和分类

### 📝 [代码风格指南](mdc:.cursor/rules/code-style.mdc)
遵循统一的编码规范和最佳实践

### 🤖 [AI开发辅助](mdc:.cursor/rules/ai-assistance.mdc)
AI辅助开发的规范和模板

### 🔍 [故障排除指南](mdc:.cursor/rules/troubleshooting.mdc)
解决常见开发问题和调试技巧

## 🚀 快速开始

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm start
```

### 构建组件库
```bash
pnpm build
```

## 📖 重要文件

- [package.json](mdc:package.json) - 项目配置和依赖
- [src/index.ts](mdc:src/index.ts) - 组件库主入口
- [README.md](mdc:README.md) - 项目说明文档
- [tsconfig.json](mdc:tsconfig.json) - TypeScript配置

## 🎯 开发原则

1. **一致性**: 遵循统一的代码风格和命名规范
2. **可维护性**: 编写清晰、可读的代码和文档
3. **可扩展性**: 设计灵活的组件API和架构
4. **性能优化**: 关注组件性能和包体积
5. **用户体验**: 提供良好的开发者体验和用户体验

## 💡 开发提示

- 使用 `pnpm start` 启动开发环境
- 参考 [Button组件](mdc:src/components/button/index.tsx) 作为开发模板
- 遵循 [代码风格指南](mdc:.cursor/rules/code-style.mdc) 进行开发
- 查看 [AI开发辅助](mdc:.cursor/rules/ai-assistance.mdc) 获取AI协助
- 参考 [组件目录](mdc:.cursor/rules/component-catalog.mdc) 了解所有可用组件

## 🔄 规则更新

这些规则会根据项目发展持续更新。如有建议或问题，请参考 [故障排除指南](mdc:.cursor/rules/troubleshooting.mdc) 或联系项目维护者。
