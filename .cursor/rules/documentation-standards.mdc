---
description:
globs:
alwaysApply: false
---
# 文档开发标准

## 概述
EUI组件库的文档开发必须遵循统一的标准，确保文档的完整性和一致性。

## 核心文件

### 1. index.md - 主文档文件 ⚠️ 必须
每个组件都必须创建`index.md`文件，这是Dumi文档系统的入口文件。

**标准模板：**
```markdown
# ComponentName 组件名 ✅

组件的简短描述。

## 何时使用

- 使用场景1
- 使用场景2
- 使用场景3

## 代码演示

<code src="./demo/basic.tsx"></code>
<code src="./demo/advanced.tsx"></code>
<code src="./demo/table.tsx"></code>

## API

### ComponentName

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| prop1 | 属性说明 | `string` | - |
| prop2 | 属性说明 | `boolean` | `false` |

### ComponentRef

| 属性 | 说明 | 类型 |
| --- | --- | --- |
| nativeElement | 原生DOM元素 | `HTMLElement \| null` |
```

### 2. README.md - 开发文档
提供详细的开发说明和使用指南，主要面向开发者。

## Demo引入规范

### 1. 使用code标签
```markdown
<code src="./demo/basic.tsx"></code>
<code src="./demo/advanced.tsx"></code>
```

### 2. Demo文件命名规范
- `basic.tsx` - 基础用法演示
- `advanced.tsx` - 高级功能演示
- `table.tsx` - 表格中使用场景
- `form.tsx` - 表单中使用场景
- `size.tsx` - 尺寸变化演示
- `theme.tsx` - 主题变化演示

### 3. Demo顺序规范
按照从简单到复杂的顺序引入demo：
1. 基础用法
2. 属性变化（尺寸、主题等）
3. 交互功能
4. 特殊场景（表格、表单等）
5. 高级功能

## API文档规范

### 1. 组件属性表格
```markdown
| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| prop | 属性说明 | `PropType` | `defaultValue` |
```

### 2. 类型定义规范
- 使用反引号包装类型：`string`、`boolean`、`number`
- 联合类型使用：`'option1' \| 'option2'`
- 可选属性标注：`string?` 或在说明中注明
- 复杂类型引用：`ComponentProps`、`ReactNode`

### 3. 默认值规范
- 字符串默认值：`'default'`
- 布尔值默认值：`true`、`false`
- 数字默认值：`24`、`0`
- 无默认值：`-`

## 文档内容规范

### 1. 标题规范
```markdown
# ComponentName 组件名 ✅
```
- 使用中英文组合
- 末尾添加 ✅ 表示组件已完成

### 2. 描述规范
- 第一段：简洁的组件描述
- 何时使用：列出3-5个使用场景
- 避免过于技术性的描述

### 3. API文档完整性
确保包含：
- 所有公开属性
- 回调函数的参数说明
- Ref方法（如果有）
- 类型定义接口

## 示例参考

### 完整示例
参考以下组件的文档实现：
- [Button文档](mdc:src/components/button/index.md) - 基础组件示例
- [Table文档](mdc:src/components/table/index.md) - 复杂组件示例
- [Attachment文档](mdc:src/components/attachment/index.md) - 最新标准示例

### 常见模式
```markdown
## 代码演示

<code src="./demo/basic.tsx"></code>
<code src="./demo/size.tsx"></code>
<code src="./demo/disabled.tsx"></code>
<code src="./demo/advanced.tsx"></code>
```

## 注意事项

### 1. 文件位置
- `index.md` 必须放在组件根目录
- 与 `index.tsx` 同级

### 2. 路径引用
- demo文件使用相对路径：`./demo/basic.tsx`
- 不要使用绝对路径

### 3. 文档同步
- API文档必须与组件实际接口保持同步
- 新增属性时同步更新文档
- 废弃属性时添加废弃标记

### 4. 多语言支持
- 目前主要支持中文
- 组件名使用中英文对照
- 属性说明使用中文

## 检查清单

新增组件文档时，确保：
- ✅ 创建了 `index.md` 文件
- ✅ 引入了所有demo文件
- ✅ 提供了完整的API文档
- ✅ 包含了使用场景说明
- ✅ 文档格式符合规范
- ✅ 与组件实际功能保持一致
