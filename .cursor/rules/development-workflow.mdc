---
description: 
globs: 
alwaysApply: false
---
# 开发工作流指南

## 开发环境设置

### 环境要求
- Node.js >= 16.0.0
- pnpm >= 7.4.0
- 推荐使用 VS Code + TypeScript 插件

### 安装依赖
```bash
pnpm install
```

## 新增组件开发流程

### 1. 创建组件目录结构
```bash
mkdir -p src/components/[component-name]/{demo,style,__tests__}
```

### 2. 开发组件文件
- `index.tsx` - 组件主文件
- `index.md` - 组件文档和demo引入 (必须创建)
- `style/index.less` - 组件样式
- `demo/` - 演示文件 (必须使用DemoBlock)
- `__tests__/index.test.tsx` - 单元测试

### 3. Demo开发规范 ⚠️ 重要
所有demo文件必须遵循以下规范：
```typescript
import React from 'react';
import { DemoBlock } from 'demos';
import { ComponentName, Space } from '@hose/eui';

export default () => {
  return (
    <>
      <DemoBlock title="基础用法">
        <Space size={16} align="center">
          <ComponentName />
        </Space>
      </DemoBlock>
    </>
  );
};
```

### 4. 更新配置文件 ⚠️ 重要
- **config/components.ts**: 添加组件路径到对应分类
- **src/index.ts**: 导出组件和类型定义

### 5. 本地测试
```bash
pnpm start          # 启动开发服务器验证
pnpm test           # 运行单元测试
pnpm lint           # 代码质量检查
```

### 6. 提交代码
```bash
git add .
git commit -m "feat: add [component-name] component"
```

## 常用开发命令

### 启动开发服务器
```bash
pnpm start          # 启动Dumi开发服务器
```

### 构建相关
```bash
pnpm build          # 构建组件库
pnpm build-doc      # 构建文档
```

### 代码质量
```bash
pnpm lint           # ESLint检查
pnpm format         # 代码格式化
pnpm eslint         # 完整ESLint检查
pnpm stylelint      # 样式检查
```

### 测试相关
```bash
pnpm test           # 运行测试
pnpm test:ci        # CI环境测试
pnpm test-with-coverage  # 带覆盖率的测试
```

### 发布相关
```bash
pnpm publish        # 发布到npm
pnpm publish:alpha  # 发布alpha版本
pnpm publish:beta   # 发布beta版本
```

## Git工作流

### 提交规范
项目使用 ](mdc:commitlint.config.js) 进行提交信息规范：
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 样式调整
refactor: 重构
test: 测试相关
chore: 构
```

### 代码检查
项目配置了 [Husky](mdc:.husky) 进行Git钩子管理：
- pre-commit: 代码格式化和lint检查
- commit-msg## 构建系统

### Gulp构建
主要构建配置在 [gulpfile.js](mdc:gulpfile.js)：
- ES模块构建 (lib/es/)
- C/cjs/)
- UMD构建 (lib/umd/)

### TypeScript配置
[tsconfig.json](mdc:tsconfig.json) 配置了：
- 路径映射
- 类型检查
- 编译选项

## 测试策略

### 测试框架
- **Jest**: 单ing-library/react**: React组件测试
- **jest-axe**: 无障碍测试

### 测试配置
[jest.config.js](mdc:jest.config.js) 包含：
- 测试环境配置
- 覆盖率设置
- 模块映射

## 文档系统

### Dumi文档
- 使用Dumi构建组件文档
- 支持MDX格式
- 自动生成API文档

### 组件演示
每个组件的demo目录包含：
- 基础用法示例
- 高级功能演示
- API参数说明

## 发布流程

1. 更新版本号
2. 生成changelog
3. 运行完整测试
4. 构建组件库
5. 发布到npm
6. 上传到CDN