---
description: 
globs: 
alwaysApply: false
---
# EUI组件库项目概览

## 项目简介
EUI是基于合思Design System 3.0建立的一套标准React组件库，提供了丰富的UI组件和完整的设计系统。

## 核心信息
- **包名**: @hose/eui
- **当前版本**: 3.9.3
- **技术栈**: React 18 + TypeScript + Less + Dumi
- **包管理器**: pnpm (>=7.4.0)
- **Node版本**: >=16.0.0

## 项目结构
- [src/](mdc:src) - 源代码目录
  - [components/](mdc:src/components) - 所有UI组件
  - [global/](mdc:src/global) - 全局样式和配置
  - [utils/](mdc:src/utils) - 工具函数
  - [demos/](mdc:src/demos) - 演示组件
  - [tests/](mdc:src/tests) - 测试相关
- [lib/](mdc:lib) - 构建输出目录
- [docs/](mdc:docs) - 文档目录
- [scripts/](mdc:scripts) - 构建脚本

## 主要配置文件
- [package.json](mdc:package.json) - 项目配置和依赖
- [tsconfig.json](mdc:tsconfig.json) - TypeScript配置
- [gulpfile.js](mdc:gulpfile.js) - 构建配置
- [jest.config.js](mdc:jest.config.js) - 测试配置

## 兼容性
- **React**: ^16.8.0 | ^17.0.0 | ^18.0.0
- **TypeScript**: >= 3.8
- **浏览器**: iOS Safari >= 10, Chrome >= 49
