---
description:    EUI组件库开发助手：提供组件开发模板、样式系统规范、代码风格指南，帮助快速理解项目结构和开发标准
globs: 
alwaysApply: false
---
# EUI 组件库开发指南

EUI是基于合思Design System 3.0的React组件库，提供完整的UI组件和设计系统。

## 🚀 快速开始

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm start

# 构建组件库
pnpm build
```

## 📁 项目结构

- `src/components/` - 所有UI组件
- `src/global/` - 全局样式和配置  
- `src/utils/` - 工具函数
- `lib/` - 构建输出

## 🔧 开发规范

### 组件开发
- 参考 [Button组件](mdc:src/components/button/index.tsx) 作为标准模板
- 使用 `forwardRef` + `mergeProps` + `withNativeProps`
- 遵循 `eui-[component-name]` 命名规范

### 样式系统
- CSS变量用于设计token：`--eui-primary-pri-500`
- Less变量用于组件逻辑
- 支持主题切换和响应式设计

### 类型定义
```typescript
export type ComponentProps = {
  // 组件属性
} & NativeProps<never>;

export type ComponentRef = HTMLElement;
```

## 📦 组件分类

**数据录入**: Button, Input, Select, Form, Upload
**数据展示**: Table, List, Card, Tree, Image  
**反馈**: Alert, Message, Modal, Drawer, Spin
**导航**: Menu, Breadcrumb, Pagination, Steps
**布局**: Grid, Layout, Space, Divider

## 🧪 测试要求

- 每个组件需要单元测试
- 使用 `@testing-library/react`
- 确保测试覆盖率

## 📚 更多资源

- [组件开发详细指南](mdc:.cursor/rules/component-development.mdc)
- [样式系统指南](mdc:.cursor/rules/styling-system.mdc)  
- [开发工作流](mdc:.cursor/rules/development-workflow.mdc)
- [故障排除](mdc:.cursor/rules/troubleshooting.mdc)

## 💡 开发提示

- 所有组件必须在 [src/index.ts](mdc:src/index.ts) 中导出
- 样式文件放在 `style/index.less`
- 演示文件放在 `demo/` 目录
- 遵循BEM命名和无障碍设计原则

# EUI组件库开发助手

## 快速导航

### 📋 项目概览
- [项目结构和架构](mdc:.cursor/rules/project-overview.mdc)
- [组件目录](mdc:.cursor/rules/component-catalog.mdc)

### 🛠️ 开发指南
- [组件开发规范](mdc:.cursor/rules/component-development.mdc)
- [Demo开发标准](mdc:.cursor/rules/demo-standards.mdc)
- [文档开发标准](mdc:.cursor/rules/documentation-standards.mdc)
- [样式系统指南](mdc:.cursor/rules/styling-system.mdc)
- [代码风格规范](mdc:.cursor/rules/code-style.mdc)

### 🔄 工作流程
- [开发工作流](mdc:.cursor/rules/development-workflow.mdc)
- [故障排除](mdc:.cursor/rules/troubleshooting.mdc)

### 🤖 AI辅助
- [AI开发指南](mdc:.cursor/rules/ai-assistance.mdc)

## 核心原则

1. **一致性优先** - 遵循统一的设计规范和代码标准
2. **组件化思维** - 构建可复用、可组合的组件
3. **类型安全** - 完整的TypeScript类型定义
4. **用户体验** - 注重可访问性和交互体验
5. **文档完整** - 提供清晰的API文档和使用示例

## 快速开始

新增组件开发清单：
1. ✅ 创建组件目录结构
2. ✅ 开发组件主文件 (使用forwardRef + mergeProps)
3. ✅ 编写样式文件 (遵循BEM命名)
4. ✅ 创建Demo文件 (必须使用DemoBlock)
5. ✅ 创建index.md文件 (引入demo和API文档)
6. ✅ 编写单元测试
7. ✅ 更新配置文件 (components.ts + index.ts)

## 重要提醒

- 新增组件必须更新 `