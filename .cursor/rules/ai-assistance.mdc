---
description:
globs:
alwaysApply: false
---
# AI 开发辅助指南

## 组件生成规范

### 创建新组件时
1. **分析需求**: 明确组件的功能、API设计和使用场景
2. **参考现有组件**: 以 [Button组件](mdc:src/components/button/index.tsx) 为模板
3. **遵循命名规范**: 使用PascalCase命名，文件夹使用kebab-case
4. **完整的文件结构**:
   ```
   src/components/[component-name]/
   ├── index.tsx          # 组件主文件
   ├── style/index.less   # 样式文件
   ├── demo/             # 演示文件
   └── __tests__/        # 测试文件
   ```

### 组件代码模板
```typescript
import React, { forwardRef } from 'react';
import classNames from 'classnames';
import { mergeProps } from '../../utils/with-default-props';
import { NativeProps, withNativeProps } from '../../utils/native-props';
import './style/index.less';

const classPrefix = 'eui-[component-name]';

export type [ComponentName]Props = {
  // 组件属性定义
} & NativeProps<never>;

export type [ComponentName]Ref = HTMLElement;

const defaultProps: [ComponentName]Props = {
  // 默认属性
};

const [ComponentName] = forwardRef<HTMLElement, [ComponentName]Props>((p, ref) => {
  const props = mergeProps(defaultProps, p);
  
  return withNativeProps(
    props,
    <div ref={ref} className={classNames(classPrefix)}>
      {/* 组件内容 */}
    </div>
  );
});

if (process.env.NODE_ENV !== 'production') {
  [ComponentName].displayName = '[ComponentName]';
}

export default [ComponentName];
```

## 样式生成规范

### CSS变量优先
```less
@class-prefix: ~'eui-[component-name]';

.@{class-prefix} {
  // 定义CSS变量
  --color: var(--eui-text-title);
  --background: var(--eui-bg-body);
  --border-radius: var(--eui-radius-s);
  
  // 使用CSS变量
  color: var(--color);
  background: var(--background);
  border-radius: var(--border-radius);
  
  // 状态变化
  &:hover {
    --color: var(--eui-primary-pri-500);
  }
  
  &:disabled {
    --color: var(--eui-text-disabled);
  }
}
```

### 主题支持
```less
// 支持多主题
&-primary {
  &-default {
    --color: var(--eui-primary-pri-500);
  }
  &-highlight {
    --color: var(--eui-primary-pri-600);
  }
  &-danger {
    --color: var(--eui-function-danger-500);
  }
}
```

## 代码优化建议

### 性能优化
- 使用 `useMemo` 缓存复杂计算
- 使用 `useCallback` 缓存事件处理函数
- 避免在render中创建新对象/函数
- 合理使用 `React.memo` 包装组件

### 类型安全
- 优先使用 `interface` 定义Props
- 使用联合类型定义枚举值
- 避免使用 `any` 类型
- 为事件处理函数添加正确的类型注解

### 无障碍性
- 添加适当的 `aria-*` 属性
- 支持键盘导航
- 提供语义化的HTML结构
- 考虑屏幕阅读器用户

## 测试生成规范

### 基础测试模板
```typescript
import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import [ComponentName] from '../index';

describe('[ComponentName]', () => {
  it('renders correctly', () => {
    render(<[ComponentName]>Test<[ComponentName]>);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const handleClick = jest.fn();
    render(<[ComponentName] onClick={handleClick}>Click me<[ComponentName]>);
    
    await userEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies custom className', () => {
    render(<[ComponentName] className="custom">Test<[ComponentName]>);
    expect(screen.getByText('Test')).toHaveClass('custom');
  });
});
```

## 文档生成规范

### API文档
- 为每个Props添加清晰的描述
- 提供使用示例
- 说明默认值和可选值
- 包含TypeScript类型定义

### Demo示例
- 基础用法示例
- 不同状态的展示
- 高级功能演示
- 最佳实践示例

## 错误处理指导

### 常见问题解决
1. **类型错误**: 检查Props定义和使用
2. **样式问题**: 验证CSS变量定义和Less编译
3. **构建错误**: 检查导入路径和导出语句
4. **测试失败**: 确认测试环境和模拟数据

### 调试建议
- 使用React DevTools检查组件状态
- 使用浏览器开发者工具调试样式
- 添加console.log进行状态追踪
- 使用TypeScript编译器检查类型错误

## 代码审查要点

### 检查清单
- [ ] 组件是否遵循命名规范
- [ ] 是否正确使用forwardRef
- [ ] 是否添加了displayName
- [ ] 样式是否使用CSS变量
- [ ] 是否支持主题切换
- [ ] 是否有完整的TypeScript类型
- [ ] 是否有单元测试
- [ ] 是否在index.ts中导出
- [ ] 是否符合无障碍性要求

### 性能检查
- [ ] 是否避免了不必要的重渲染
- [ ] 是否正确使用了React Hooks
- [ ] 是否有内存泄漏风险
- [ ] 是否优化了包体积
