---
description: 
globs: 
alwaysApply: false
---
# Demo开发标准

## 概述
EUI组件库的demo开发必须遵循统一的标准，确保文档的一致性和可维护性。

## 核心原则

### 1. 使用DemoBlock包装
所有demo内容必须使用 `DemoBlock` 组件包装，提供统一的视觉样式。

### 2. 标准导入方式
```typescript
import React from 'react';
import { DemoBlock } from 'demos';
import { ComponentName, Space } from '@hose/eui';  // 从@hose/eui导入EUI组件
import type { ComponentProps } from '@hose/eui';   // 从@hose/eui导入类型定义
```

### 3. 布局规范
- 使用 `Space` 组件进行布局，避免内联样式
- 常用布局模式：
  - 水平排列: `<Space size={16} align="center">`
  - 垂直排列: `<Space direction="vertical" size={12}>`
  - 换行排列: `<Space size={16} wrap>`

## Demo文件结构

### 基础Demo文件
每个组件至少需要以下demo文件：
- `basic.tsx` - 基础用法演示
- `advanced.tsx` - 高级功能演示

### 特殊场景Demo
根据组件特性可添加：
- `table.tsx` - 表格中的使用场景
- `form.tsx` - 表单中的使用场景
- `size.tsx` - 尺寸变化演示
- `theme.tsx` - 主题变化演示

## 标准模板

### 基础Demo模板
```typescript
import React from 'react';
import { DemoBlock } from 'demos';
import { ComponentName, Space } from '@hose/eui';

export default () => {
  return (
    <>
      <DemoBlock title="基础用法">
        <Space size={16} align="center">
          <ComponentName />
        </Space>
      </DemoBlock>

      <DemoBlock title="不同尺寸">
        <Space size={16} align="center">
          <ComponentName size="small" />
          <ComponentName size="large" />
        </Space>
      </DemoBlock>
    </>
  );
};
```

### 表格Demo模板
```typescript
import React from 'react';
import { DemoBlock } from 'demos';
import { ComponentName, Table } from '@hose/eui';

const columns = [
  // 表格列定义
];

const dataSource = [
  // 表格数据
];

export default () => {
  return (
    <>
      <DemoBlock title="表格中的使用">
        <Table 
          columns={columns} 
          dataSource={dataSource} 
          pagination={false}
        />
      </DemoBlock>
    </>
  );
};
```

## 演示数据规范

### 1. 有意义的数据
使用真实、有意义的演示数据，避免无意义的占位符：
```typescript
// ✅ 好的示例
const demoData = [
  { name: '钱洗西', status: '已完成' },
  { name: '赵德霞', status: '进行中' },
];

// ❌ 避免的示例
const demoData = [
  { name: 'test1', status: 'status1' },
  { name: 'test2', status: 'status2' },
];
```

### 2. 多样化展示
提供不同类型的数据，展示组件的适应性：
```typescript
const files = [
  { fileName: 'image.jpg', type: 'image' },
  { fileName: 'document.pdf', type: 'pdf' },
  { fileName: 'spreadsheet.xlsx', type: 'excel' },
];
```

## 交互演示

### 1. 回调函数演示
```typescript
const handleClick = (item) => {
  message.info(`点击了: ${item.name}`);
};

<DemoBlock title="自定义回调">
  <ComponentName onItemClick={handleClick} />
</DemoBlock>
```

### 2. 状态管理演示
```typescript
const [value, setValue] = useState('');

<DemoBlock title="受控组件">
  <ComponentName value={value} onChange={setValue} />
</DemoBlock>
```

## 样式规范

### 1. 避免内联样式
```typescript
// ❌ 避免
<div style={{ display: 'flex', gap: 16 }}>

// ✅ 推荐
<Space size={16}>
```

### 2. 使用CSS变量
```typescript
<div style={{ color: 'var(--eui-text-caption)' }}>
```

## 最佳实践

### 1. 渐进式展示
从简单到复杂，逐步展示组件功能：
1. 基础用法
2. 属性变化
3. 交互功能
4. 高级场景

### 2. 完整性检查
确保demo覆盖组件的主要功能点：
- 所有重要属性
- 主要交互方式
- 常见使用场景
- 边界情况

### 3. 可访问性
确保demo具有良好的可访问性：
- 合理的颜色对比度
- 键盘导航支持
- 屏幕阅读器友好

## 参考示例
- [Button Demo](mdc:src/components/button/demo/demo1.tsx) - 完整的基础组件demo
- [Table Demo](mdc:src/components/table/demo) - 复杂组件demo
- [Attachment Demo](mdc:src/components/attachment/demo) - 最新优化的demo示例
