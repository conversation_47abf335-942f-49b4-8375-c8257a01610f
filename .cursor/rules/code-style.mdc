---
description:
globs:
alwaysApply: false
---
# 代码风格指南

## TypeScript 规范

### 类型定义
```typescript
// ✅ 推荐：使用接口定义组件Props
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'small' | 'medium' | 'large';
  children: React.ReactNode;
}

// ✅ 推荐：使用联合类型
type ButtonVariant = 'primary' | 'secondary' | 'ghost';

// ❌ 避免：使用any类型
const handleClick = (event: any) => {}; // 不推荐
```

### 组件定义
```typescript
// ✅ 推荐：使用forwardRef
const Button = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const mergedProps = mergeProps(defaultProps, props);
  // ...
});

// ✅ 推荐：添加displayName
Button.displayName = 'Button';
```

## React 最佳实践

### Hooks使用
```typescript
// ✅ 推荐：自定义hooks以use开头
const useButtonState = (disabled: boolean) => {
  const [loading, setLoading] = useState(false);
  return { loading, setLoading };
};

// ✅ 推荐：依赖数组明确
useEffect(() => {
  // effect logic
}, [dependency1, dependency2]);
```

### 事件处理
```typescript
// ✅ 推荐：明确的事件类型
const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
  event.preventDefault();
  // handle click
};

// ✅ 推荐：异步事件处理
const handleAsyncClick = async (event: React.MouseEvent) => {
  try {
    setLoading(true);
    await someAsyncOperation();
  } finally {
    setLoading(false);
  }
};
```

## 样式规范

### CSS类命名
```less
// ✅ 推荐：BEM命名规范
.eui-button {
  &__icon {
    margin-right: 8px;
  }
  
  &--loading {
    pointer-events: none;
  }
  
  &--size-large {
    padding: 12px 24px;
  }
}
```

### CSS变量使用
```less
// ✅ 推荐：语义化变量名
.eui-button {
  --button-bg: var(--eui-primary-pri-500);
  --button-text: var(--eui-static-white);
  --button-border: transparent;
  
  background: var(--button-bg);
  color: var(--button-text);
  border-color: var(--button-border);
}
```

## 文件组织

### 导入顺序
```typescript
// 1. React相关
import React, { forwardRef, useState } from 'react';

// 2. 第三方库
import classNames from 'classnames';

// 3. 内部工具
import { mergeProps } from '../../utils/with-default-props';
import { NativeProps, withNativeProps } from '../../utils/native-props';

// 4. 样式文件
import './style/index.less';
```

### 导出规范
```typescript
// ✅ 推荐：默认导出组件，命名导出类型
export type { ButtonProps, ButtonRef };
export default Button;
```

## 注释规范

### JSDoc注释
```typescript
/**
 * 按钮组件
 * @param variant 按钮变体
 * @param size 按钮尺寸
 * @param loading 加载状态
 */
interface ButtonProps {
  /** 按钮变体 */
  variant?: 'primary' | 'secondary';
  /** 按钮尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 是否显示加载状态 */
  loading?: boolean;
}
```

### 代码注释
```typescript
// ✅ 推荐：解释为什么，而不是做什么
// 使用setTimeout确保DOM更新后再聚焦
setTimeout(() => {
  inputRef.current?.focus();
}, 0);

// ❌ 避免：显而易见的注释
// 设置loading为true
setLoading(true);
```

## 性能优化

### 避免不必要的重渲染
```typescript
// ✅ 推荐：使用useMemo缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(props.data);
}, [props.data]);

// ✅ 推荐：使用useCallback缓存函数
const handleClick = useCallback((id: string) => {
  onItemClick?.(id);
}, [onItemClick]);
```

### 懒加载
```typescript
// ✅ 推荐：大型组件使用懒加载
const LazyChart = lazy(() => import('./Chart'));

// 使用时包装在Suspense中
<Suspense fallback={<Spin />}>
  <LazyChart data={data} />
</Suspense>
```

## 错误处理

### 边界情况
```typescript
// ✅ 推荐：处理边界情况
const Button = ({ children, ...props }) => {
  if (!children && !props.icon) {
    console.warn('Button: 按钮需要children或icon属性');
  }
  
  return (
    <button {...props}>
      {children}
    </button>
  );
};
```

### 错误边界
```typescript
// ✅ 推荐：为复杂组件添加错误边界
class ComponentErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Component error:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <div>组件渲染出错</div>;
    }
    return this.props.children;
  }
}
```
