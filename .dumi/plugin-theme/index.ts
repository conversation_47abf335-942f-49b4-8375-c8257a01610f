import type { IApi } from '@umijs/types'

const COLOR_HEAD_SCP = `
(function () {
  var cache = localStorage.getItem('dumi:prefers-color') ?? 'auto';
  var isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  var enums = ['light', 'dark', 'auto'];

  document.documentElement.setAttribute(
    'data-prefers-color',
    cache === enums[2]
      ? (isDark ? enums[1] : enums[0])
      : cache
  );
})();
`

export default (api: IApi) => {
  // api.addHTMLHeadScripts(async () => [{ content: COLOR_HEAD_SCP }])
}
