import React, { useEffect, useContext, useRef } from 'react';
import { ConfigTheme } from '@hose/eui-theme';

import { ConfigProvider } from '@hose/eui';
import zhCN from '@hose/eui/es/locale/zh_CN';

export default function ({ children }) {
	// 开启主题
	useEffect(() => {
		// 启用 eui 主题
		document.documentElement.setAttribute('theme-platform', 'eui');

		const control = new ConfigTheme();
		control.config({ prefix: 'eui', platform: 'eui' });
	}, []);

	// 自动切换颜色
	useEffect(() => {
		const enums = ['light', 'dark', 'auto'];
		const cache = localStorage.getItem('dumi:prefers-color') ?? enums[2];
		const darkMode = window.matchMedia('(prefers-color-scheme: dark)');

		// 监听主题切换事件
		if (cache === enums[2]) {
			document.body.setAttribute('theme-mode', darkMode.matches ? enums[1] : enums[0]);
			darkMode?.addEventListener('change', (e) => {
				document.body.setAttribute('theme-mode', e.matches ? enums[1] : enums[0]);
			});
		} else {
			document.body.setAttribute('theme-mode', cache);
		}
	}, []);

	return <ConfigProvider locale={zhCN}>{children}</ConfigProvider>;
}
