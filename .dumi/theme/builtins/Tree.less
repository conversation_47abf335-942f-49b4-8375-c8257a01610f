@import (reference) '../style/variables.less';
@treePrefixCls: ~'@hose/eui-rc-tree';
@treeNodePrefixCls: ~'@{treePrefixCls}-treenode';

.__dumi-site-tree {
  padding: 16px;
  background-color: @c-light-bg;
  border: 1px solid @c-border;
  border-radius: 2px;

  [data-prefers-color='dark'] & {
    color: @c-text-dark;
    background-color: @c-light-bg-dark;
    border-color: @c-border-dark;
  }

  small {
    padding-left: 24px;
    color: @c-secondary;
    font-size: 14px;

    [data-prefers-color='dark'] & {
      color: @c-secondary-dark;
    }

    &::before {
      content: '# ';
    }
  }

  &.@{treePrefixCls} {
    transition: all 0.3s;
    line-height: 1.5715;
    list-style: none;
    font-size: 14px;

    .@{treeNodePrefixCls} {
      position: relative;
      display: flex;
      align-items: flex-start;
      padding-bottom: 4px;
      &:before {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 4px;
        left: 0;
        transition: background 0.3s;
        content: '';
      }
      &:hover:before {
        background: #f5f5f5;

        [data-prefers-color='dark'] & {
          background: @c-light-bg-dark;
        }
      }
      > * {
        z-index: 1;
      }
    }

    .@{treeNodePrefixCls} span.@{treePrefixCls}-switcher {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      margin-right: 2px;
      line-height: 24px;
      background: transparent;
      cursor: pointer;
    }

    .@{treeNodePrefixCls} .@{treePrefixCls}-node-content-wrapper {
      flex: auto;
      min-height: 24px;
      margin: 0;
      padding: 0 4px;
      line-height: 24px;
      cursor: pointer;
      .@{treePrefixCls}-iconEle {
        margin-right: 6px;
        vertical-align: 0;
      }
    }

    &.@{treePrefixCls}-show-line {
      .@{treePrefixCls}-indent {
        display: inline-block;
        height: 0;
        vertical-align: bottom;

        &-unit {
          position: relative;
          display: inline-block;
          width: 24px;
          height: 24px;

          &::before {
            position: absolute;
            top: 0;
            right: 12px;
            bottom: -4px;
            border-right: 1px solid #d9d9d9;
            content: '';
          }

          &-end::before {
            display: none;
          }
        }
      }

      .tree-switcher-leaf-line {
        position: relative;
        z-index: 1;
        display: inline-block;
        width: 100%;
        height: 100%;
        text-align: center;
        &::before {
          position: absolute;
          top: 0;
          bottom: -4px;
          margin-left: -1px;
          border-left: 1px solid #d9d9d9;
          content: ' ';
        }

        &::after {
          position: absolute;
          top: 0;
          bottom: 10px;
          width: 12px;
          margin-left: -1px;
          border-bottom: 1px solid #d9d9d9;
          content: ' ';
        }
      }

      .@{treeNodePrefixCls}-leaf-last {
        .tree-switcher-leaf-line::before {
          height: 14px;
        }
      }
    }
  }

  &-icon {
    width: 16px;
    color: @c-text;
    vertical-align: -0.225em;

    [data-prefers-color='dark'] & {
      color: @c-text-dark;
    }
  }
}
