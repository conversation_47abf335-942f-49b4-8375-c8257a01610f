@import (reference) '../style/variables.less';

.@{prefix}-example-wrapper {
  padding-top: 16px;
  height: calc(100vh - @s-nav-height - 2px);

  @media @mobile {
    padding-top: 16px;
  }

  &-toolbar {
    display: flex;
    color: @c-heading;
    font-size: 16px;
    font-weight: 500;
    justify-content: space-between;
    margin-bottom: 16px;
    border-bottom: 1px solid @c-border;

    button,
    a {
      display: inline-block;
      margin: 6px 0 6px 12px;
      width: 16px;
      height: 16px;
      font-weight: 400;
      border: 0;
      outline: none;
      vertical-align: middle;
      cursor: pointer;

      &:first-child {
        background-position-x: -144px;
      }

      &:nth-child(2) {
        background-position-x: -126px;
      }
    }
  }

  iframe {
    width: 100%;
    min-height: 100%;
    border: 0;
  }
}
