@import (reference) '../style/variables.less';

.@{prefix}-previewer {
  background-color: #fff;
  border: 1px solid @c-border;
  border-radius: 1px;

  [data-prefers-color=dark] & {
    background-color: @c-bg-dark;
    border-color: @c-border-dark;
  }

  &[data-debug] {
    margin-top: 32px;
    border-color: #ffcb00;

    &::before {
      content: 'DEV ONLY';
      float: left;
      margin-left: -1px;
      margin-top: -18px;
      padding: 3px 6px;
      font-size: 12px;
      line-height: 1;
      background-color: #ffcb00;
      color: #735600;
      text-shadow: 0.5px 0.5px 0 rgba(255, 255, 255, 0.5);
      border-top-left-radius: 1px;
      border-top-right-radius: 1px;
    }
  }

  &[data-iframe] {
    .@{prefix}-previewer-browser-nav {
      padding: 2px 6px;
      background-color: @c-border;

      &::before {
        @s-btn: 12px;
        @s-btn-gap: 8px;

        content: '';
        display: inline-block;
        width: @s-btn;
        height: @s-btn;
        border-radius: 50%;
        background-color: #fd6458;
        box-shadow: (@s-btn + @s-btn-gap) 0 0 #ffbf2b, (@s-btn + @s-btn-gap) * 2 0 0 #24cc3d;
      }
    }

    .@{prefix}-previewer-demo > iframe {
      border: 0;
      width: 100%;
      height: 300px;
    }
  }

  + .@{prefix}-previewer {
    margin-top: 32px;
  }

  &-demo {
    padding: 40px 24px;
  }

  &-target {
    border-color: fade(@c-primary, 50%);
    box-shadow: 0 0 0 5px fade(@c-primary, 5%);

    [data-prefers-color=dark] & {
      box-shadow: 0 0 0 5px fade(@c-primary, 30%);
    }
  }

  &-desc {
    > div:last-child {
      padding: 1.2em 1em 1em;
      color: @c-text;
      border-top: 1px solid @c-border;

      [data-prefers-color=dark] & {
        border-color: @c-border-dark;
      }
    }

    .markdown > p:first-child {
      margin-top: 0;
    }

    .markdown > p:last-child {
      margin-bottom: 0;
    }

    &[data-title] {
      position: relative;

      > a:first-child {
        position: absolute;
        top: 0;
        left: 1em;
        margin-left: -4px;
        padding: 0 4px;
        color: @c-heading;
        font-size: inherit;
        font-weight: 500;
        background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0)) 100%;
        transform: translateY(-50%);
        pointer-events: auto;
        cursor: pointer;

        [data-prefers-color=dark] & {
          color: @c-heading-dark;
          background: linear-gradient(to top, @c-bg-dark, @c-bg-dark 50%, rgba(255, 255, 255, 0)) 100%;
        }
      }

      &:empty {
        padding-top: 0;

        // modify action area style when only has title field
        + .@{prefix}-previewer-actions {
          height: 46px;
          border-top-style: solid;
        }
      }
    }
  }

  &-actions {
    display: flex;
    height: 40px;
    padding: 0 1em;
    align-items: center;
    border-top: 1px dashed @c-border;

    [data-prefers-color=dark] & {
      border-color: @c-border-dark;
    }

    > a:not(:last-child),
    > button:not(:last-child) {
      margin-right: 8px;
    }

    > a {
      display: flex;
    }

    button {
      position: relative;
      display: inline-block;
      width: 16px;
      height: 16px;
      padding: 0;
      border: 0;
      box-sizing: border-box;
      cursor: pointer;
      opacity: 0.6;
      outline: none;
      transition: opacity 0.2s, background 0.2s;

      [data-prefers-color=dark] & {
        opacity: 1;
      }

      // expand click area
      &::after {
        content: '';
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
      }

      &:hover {
        opacity: 0.8;
      }

      &:active {
        opacity: 0.9;
      }

      &:disabled {
        opacity: 0.2;
        cursor: not-allowed;
      }

      &[role='codesandbox'] {
        background-position: -18px 0;
      }

      &[role='codepen'] {
        background-position: -36px 0;
      }

      &[role='source'] {
        background-position: -72px 0;
      }

      &[role='change-jsx'] {
        background-position: -90px 0;
      }

      &[role='change-tsx'] {
        background-position: -108px 0;
      }

      &[role='open-demo'] {
        background-position: -126px 0;
      }

      &[role='motions'] {
        background-position: -162px 0;
      }

      &[role='sketch-component'] {
        background-position: -182px 0;
      }

      &[role='sketch-group'] {
        background-position: -200px 0;
      }

      &[role='copy'][data-status='ready'] {
        background-position: -54px 0;
      }

      &[role='copy'][data-status='copied'] {
        pointer-events: none;
        background-position: -54px -16px;
      }

      &[role='refresh'] {
        background-position-x: -144px;
      }
    }

    // split action buttons by a blank node
    > span {
      flex: 1;
      display: inline-block;
    }
  }

  &-source {
    border-top: 1px dashed @c-border;

    [data-prefers-color=dark] & {
      border-color: @c-border-dark;
    }

    &-tab {
      border-top: 1px dashed @c-border;

      [data-prefers-color=dark] & {
        border-color: @c-border-dark;
      }

      .@{prefix}-tabs-tab-btn {
        position: relative;
        padding-left: 32px;

        &::before,
        &::after {
          content: '';
          position: absolute;
          margin-right: 4px;
          display: inline-block;
          box-sizing: border-box;
        }

        &::before {
          left: 16px;
          top: 50%;
          margin-top: -6px;
          width: 10px;
          height: 12px;
          border: 1px solid @c-secondary;
        }

        &::after {
          top: 50%;
          left: 23px;
          margin-top: -7px;
          width: 4px;
          height: 4px;
          background: #fff;
          border-bottom: 1px solid @c-secondary;
          transform: rotate(45deg);
          [data-prefers-color=dark] & {
            background: @c-bg-dark;
          }
        }
      }
    }
  }
}

.@{prefix}-tabs {
  overflow: hidden;

  &.@{prefix}-tabs-top {
    flex-direction: column;

    .@{prefix}-tabs-ink-bar {
      bottom: 0;
    }
  }

  &-nav {
    display: flex;

    &-wrap {
      display: flex;
      white-space: nowrap;
      overflow: hidden;

      &&-ping-left {
        box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1) inset;
      }

      &&-ping-right ~ * > .@{prefix}-tabs-nav-more {
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
      }
    }

    &-list {
      position: relative;
      display: flex;
      transition: transform 0.2s;
    }

    &-more {
      height: 100%;
      cursor: pointer;
      background: none;
      border: 0;
      transition: box-shadow 0.2s;
    }
  }

  &-tab {
    display: flex;

    &-btn {
      padding: 0 16px;
      font-size: 14px;
      line-height: 36px;
      border: 0;
      outline: none;
      background: transparent;
      box-sizing: border-box;
      cursor: pointer;

      &:hover {
        color: @c-primary;
      }
    }
  }

  &-ink-bar {
    position: absolute;
    height: 2px;
    background: @c-primary;
    transition: left 0.2s, width 0.2s;
    pointer-events: none;

    [data-prefers-color=dark] & {
      background: @c-primary-dark;
    }
  }

  &-dropdown {
    position: absolute;
    background: #fff;
    border: 1px solid @c-border;
    max-height: 200px;
    overflow: auto;

    > ul {
      list-style: none;
      margin: 0;
      padding: 0;

      > li {
        padding: 4px 12px;
        font-size: 14px;
        cursor: pointer;

        &:hover {
          color: @c-primary;
        }

        &:not(:last-child) {
          border-bottom: 1px dashed @c-border;
        }
      }
    }

    &-hidden {
      display: none;
    }
  }
}
