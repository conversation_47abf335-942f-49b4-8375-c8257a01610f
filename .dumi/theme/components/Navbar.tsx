import { FC, MouseEvent, useCallback, useEffect, useState } from 'react';
import React, { useContext } from 'react';
import { context, Link, NavLink } from 'dumi/theme';
import { OutlinedDirectionDown, OutlinedGeneralLoading } from '@hose/eui-icons';
import { Dropdown } from '@hose/eui';
import { compare } from 'compare-versions';
import pkg from '../../../package.json';
import './Navbar.less';

interface INavbarProps {
	location: any;
	navPrefix?: React.ReactNode;
	darkPrefix?: React.ReactNode;
	onMobileMenuClick: (ev: MouseEvent<HTMLButtonElement>) => void;
}

const Navbar: FC<INavbarProps> = ({ onMobileMenuClick, navPrefix, location, darkPrefix }) => {
	const {
		base,
		config: { mode, title, logo },
		nav: navItems
	} = useContext(context);

	const [versionList, setVersionList] = useState([]);
	const [isLoading, setLoading] = useState(true);

	useEffect(() => {
		fetch('https://eui.serverless.ekuaibao.com/npm/@hose/eui')
			.then((res) => res.json())
			.then((res) => {
				// 3.0.25 后才有版本的静态文件
				const versions = Object.keys(res.versions)
					.filter((key) => compare(key, '3.0.25', '>='))
					.slice(-20)
					.reverse();
				const versionList = versions.map((ver) => ({ key: ver, label: ver }));
				versionList.unshift({ key: '0', label: '最新版' });
				setVersionList(versionList);
				setLoading(false);
			})
			.catch((err) => {
				console.error(err);
			});
	}, []);

	const handleSelectVersion = useCallback(({ key, domEvent }) => {
		domEvent.stopPropagation();
		let linkUrl = `https://statics.ekuaibao.com/eui/eui-web/${key}/index.html${window.location.hash}`;
		if (key === '0') {
			linkUrl = `https://eui.serverless.ekuaibao.com/${window.location.hash}`;
		}
		window.open(linkUrl);
	}, []);

	return (
		<div className="__dumi-default-navbar" data-mode={mode}>
			{/* menu toogle button (only for mobile) */}
			<button className="__dumi-default-navbar-toggle" onClick={onMobileMenuClick} />
			{/* logo & title */}
			<Link
				className="__dumi-default-navbar-logo"
				style={{
					backgroundImage: logo && `url('${logo}')`
				}}
				to={base}
				data-plaintext={logo === false || undefined}
			>
				<div className="title">{title}</div>
				<div className="version">
					<Dropdown menu={{ items: versionList, onClick: handleSelectVersion }}>
						<div>
							{`v${pkg.version} `}
							{isLoading ? <OutlinedGeneralLoading spin /> : <OutlinedDirectionDown />}
						</div>
					</Dropdown>
				</div>
			</Link>
			<nav>
				{navPrefix}
				{/* nav */}
				{navItems.map((nav) => {
					const child = Boolean(nav.children?.length) && (
						<ul>
							{nav.children.map((item) => (
								<li key={item.path}>
									<NavLink to={item.path}>{item.title}</NavLink>
								</li>
							))}
						</ul>
					);

					return (
						<span key={nav.title || nav.path}>
							{nav.path ? (
								<NavLink to={nav.path} key={nav.path}>
									{nav.title}
								</NavLink>
							) : (
								nav.title
							)}
							{child}
						</span>
					);
				})}
				<div className="__dumi-default-navbar-tool">
					{darkPrefix}
				</div>
			</nav>
		</div>
	);
};

export default Navbar;
