@import (reference) '../style/variables.less';

.@{prefix}-navbar {
  position: fixed;
  z-index: 101;
  top: 0;
  left: 0;
  right: 0;
  display: none;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  height: @s-nav-height;
  white-space: nowrap;
  background: #fff;
  box-shadow: 0 8px 24px -2px rgba(0, 0, 0, 0.05);

  [data-prefers-color=dark] & {
    background: @c-bg-dark;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.65);
  }

  @media @mobile {
    display: flex;
    justify-content: center;
    height: @s-mobile-nav-height;
  }

  &-toggle {
    position: absolute;
    top: 14px;
    left: 16px;
    display: none;
    width: 22px;
    height: 22px;
    border: 0;
    outline: none;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAAA4CAYAAAB5YT9uAAAAAXNSR0IArs4c6QAAASVJREFUeAHt3DuOwjAUBVAHscWZjaSkAIqU2QjskV80QwpsF3ArdGiw3hW3OGnQM2Iof6/dfr7+n71/LjAdx+HRsvm8SkNPAHBPJ5ABDiD2KgD3dAIZ4ABirwJwTyeQAQ4g9ioA93QC2fbZsSm/z7MDAQIECBAgQIAAAQIECBAgQIAAAQKvAsvV8mO8O8yn19jkXYHpMC7byXVdeS0/75b5XFvAwr1tE0kARxjbJYDbNpEEcISxXQK4bRNJAEcY2yWA2zaRZP0ePJRzpFEJAQIECBAgQIAAAQIECBAgQIAAgW8VWK/tj7Nb5eBTnvbjsp1c15WX4ncRQeB7lb8zyHrW29xo1F1iU8AxynoR4LpLbAo4RlkvAlx3iU0BxyjrRYDrLrHpDVSAEEPXScHTAAAAAElFTkSuQmCC')
      no-repeat center / contain;

    @media @mobile {
      display: block;
    }
  }

  &-logo {
    display: flex;
    height: 40px;
    color: #080e29;
    font-weight: 500;
    text-decoration: none;
    font-size: 24px;
    line-height: 40px;

    [data-prefers-color=dark] & {
      color: @c-heading-dark;
    }

    &:not([data-plaintext]) {
      padding-left: 56px;
      background: url(@img-logo) no-repeat 0 / contain;
    }

    .version {
      color: #a8a8ad;
      display: block;
      margin-left: 8px;
      font-size: 12px;
      padding-top: 4px;
      font-weight: 400;
    }

    @media @mobile {
      height: 28px;
      line-height: 28px;

      &:not([data-plaintext]) {
        padding-left: 36px;
      }
    }
  }

  &-tool {
    display: inline-block;
  }

  nav {
    > span {
      position: relative;
      margin-left: 40px;
      display: inline-block;
      color: @c-text;
      height: @s-nav-height;
      cursor: pointer;
      font-size: 14px;
      line-height: @s-nav-height;
      text-decoration: none;
      letter-spacing: 0;

      [data-prefers-color=dark] & {
        color: @c-text-dark;
      }

      > a {
        color: #4d5164;
        text-decoration: none;

        [data-prefers-color=dark] & {
          color: @c-heading-dark;
        }

        &:hover,
        &.active {
          color: @c-primary;
          [data-prefers-color=dark] & {
            color: @c-primary-dark;
          }
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          right: -18px;
          left: -18px;
        }

        &.active::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: -2px;
          right: -2px;
          height: 2px;
          background-color: @c-primary;
          border-radius: 1px;
        }
      }

      + *:not(a) {
        margin-left: 40px;
      }

      // second nav
      > ul {
        list-style: none;
        position: absolute;
        top: 100%;
        left: 50%;
        margin: 0;
        min-width: 100px;
        padding: 8px 18px;
        line-height: 2;
        background-color: #fff;
        box-shadow: 0 8px 24px -2px rgba(0, 0, 0, 0.08);
        transform: translate(-50%);
        transform-origin: top;
        border-radius: 1px;
        transition: all 0.2s;

        [data-prefers-color=dark] & {
          background-color: @c-bg-dark;
        }

        a {
          position: relative;
          display: block;
          color: @c-text;
          text-decoration: none;

          [data-prefers-color=dark] & {
            color: @c-text-dark;
          }

          &:hover,
          &.active {
            color: @c-primary;

            [data-prefers-color=dark] & {
              color: @c-primary-dark;
            }
          }
        }
      }

      &:not(:hover) > ul {
        visibility: hidden;
        pointer-events: none;
        transform: translate(-50%) scaleY(0.9);
        opacity: 0;
      }
    }

    .@{prefix}-search + .@{prefix}-locale-select {
      margin-left: 40px;
    }

    @media @mobile {
      > a,
      > span,
      > div {
        display: none;
      }
    }
  }

  &[data-mode='site'] {
    display: flex;
  }
}
