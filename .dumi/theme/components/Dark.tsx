import { FC, useEffect } from 'react';
import React from 'react';
import { usePrefersColor } from 'dumi/theme';
import './Dark.less';

interface darkProps {
	darkSwitch: boolean;
	isSideMenu: boolean;
	onDarkSwitchClick?: (ev) => void;
}

const Dark: FC<darkProps> = ({ darkSwitch, onDarkSwitchClick, isSideMenu }) => {
	const allState = ['light', 'dark', 'auto'];
	const [color, setColor] = usePrefersColor();
	const prefersColor = color;

	const sunSvg = (
		<svg
			viewBox="0 0 1024 1024"
			version="1.1"
			xmlns="http://www.w3.org/2000/svg"
			p-id="4026"
			width="22"
			height="22"
		>
			<path
				d="M915.2 476.16h-43.968c-24.704 0-44.736 16-44.736 35.84s20.032 35.904 44.736 35.904H915.2c24.768 0 44.8-16.064 44.8-35.904s-20.032-35.84-44.8-35.84zM512 265.6c-136.704 0-246.464 109.824-246.464 246.4 0 136.704 109.76 246.464 246.464 246.464S758.4 648.704 758.4 512c0-136.576-109.696-246.4-246.4-246.4z m0 425.6c-99.008 0-179.2-80.128-179.2-179.2 0-98.944 80.192-179.2 179.2-179.2S691.2 413.056 691.2 512c0 99.072-80.192 179.2-179.2 179.2zM197.44 512c0-19.84-19.136-35.84-43.904-35.84H108.8c-24.768 0-44.8 16-44.8 35.84s20.032 35.904 44.8 35.904h44.736c24.768 0 43.904-16.064 43.904-35.904zM512 198.464c19.776 0 35.84-20.032 35.84-44.8v-44.8C547.84 84.032 531.84 64 512 64s-35.904 20.032-35.904 44.8v44.8c0 24.768 16.128 44.864 35.904 44.864z m0 627.136c-19.776 0-35.904 20.032-35.904 44.8v44.736C476.096 940.032 492.16 960 512 960s35.84-20.032 35.84-44.8v-44.736c0-24.768-16.064-44.864-35.84-44.864z m329.92-592.832c17.472-17.536 20.288-43.072 6.4-57.024-14.016-14.016-39.488-11.2-57.024 6.336-4.736 4.864-26.496 26.496-31.36 31.36-17.472 17.472-20.288 43.008-6.336 57.024 13.952 14.016 39.488 11.2 57.024-6.336 4.8-4.864 26.496-26.56 31.296-31.36zM213.376 759.936c-4.864 4.8-26.56 26.624-31.36 31.36-17.472 17.472-20.288 42.944-6.4 56.96 14.016 13.952 39.552 11.2 57.024-6.336 4.8-4.736 26.56-26.496 31.36-31.36 17.472-17.472 20.288-43.008 6.336-56.96-14.016-13.952-39.552-11.072-56.96 6.336z m19.328-577.92c-17.536-17.536-43.008-20.352-57.024-6.336-14.08 14.016-11.136 39.488 6.336 57.024 4.864 4.864 26.496 26.56 31.36 31.424 17.536 17.408 43.008 20.288 56.96 6.336 14.016-14.016 11.264-39.488-6.336-57.024-4.736-4.864-26.496-26.56-31.296-31.424z m527.168 628.608c4.864 4.864 26.624 26.624 31.36 31.424 17.536 17.408 43.072 20.224 57.088 6.336 13.952-14.016 11.072-39.552-6.4-57.024-4.864-4.8-26.56-26.496-31.36-31.36-17.472-17.408-43.072-20.288-57.024-6.336-13.952 14.016-11.008 39.488 6.336 56.96z"
				p-id="4027"
			></path>
		</svg>
	);
	const moonSvg = (
		<svg
			viewBox="0 0 1024 1024"
			version="1.1"
			xmlns="http://www.w3.org/2000/svg"
			p-id="3854"
			width="22"
			height="22"
		>
			<path
				d="M991.816611 674.909091a69.166545 69.166545 0 0 0-51.665455-23.272727 70.795636 70.795636 0 0 0-27.438545 5.585454A415.674182 415.674182 0 0 1 754.993338 698.181818c-209.594182 0-393.472-184.785455-393.472-395.636363 0-52.363636 38.539636-119.621818 69.515637-173.614546 4.887273-8.610909 9.634909-16.756364 14.103272-24.901818A69.818182 69.818182 0 0 0 384.631156 0a70.842182 70.842182 0 0 0-27.438545 5.585455C161.678429 90.298182 14.362065 307.898182 14.362065 512c0 282.298182 238.824727 512 532.38691 512a522.286545 522.286545 0 0 0 453.957818-268.334545A69.818182 69.818182 0 0 0 991.816611 674.909091zM546.679156 954.181818c-248.785455 0-462.941091-192-462.941091-442.181818 0-186.647273 140.637091-372.829091 300.939637-442.181818-36.817455 65.629091-92.578909 151.970909-92.578909 232.727273 0 250.181818 214.109091 465.454545 462.917818 465.454545a488.331636 488.331636 0 0 0 185.181091-46.545455 453.003636 453.003636 0 0 1-393.565091 232.727273z m103.656728-669.323636l-14.266182 83.781818a34.909091 34.909091 0 0 0 50.362182 36.770909l74.775272-39.563636 74.752 39.563636a36.142545 36.142545 0 0 0 16.174546 3.956364 34.909091 34.909091 0 0 0 34.210909-40.727273l-14.289455-83.781818 60.509091-59.345455a35.025455 35.025455 0 0 0-19.223272-59.578182l-83.61891-12.101818-37.376-76.101818a34.56 34.56 0 0 0-62.254545 0l-37.376 76.101818-83.618909 12.101818a34.909091 34.909091 0 0 0-19.246546 59.578182z m70.423272-64.698182a34.280727 34.280727 0 0 0 26.135273-19.083636l14.312727-29.090909 14.336 29.090909a34.257455 34.257455 0 0 0 26.135273 19.083636l32.046546 4.887273-23.272728 22.574545a35.234909 35.234909 0 0 0-10.007272 30.952727l5.46909 32.116364-28.625454-15.127273a34.490182 34.490182 0 0 0-32.302546 0l-28.695272 15.127273 5.469091-32.116364a35.141818 35.141818 0 0 0-9.984-30.952727l-23.272728-22.574545z"
				p-id="3855"
			></path>
		</svg>
	);
	const autoSvg = (
		<svg
			viewBox="0 0 1024 1024"
			version="1.1"
			xmlns="http://www.w3.org/2000/svg"
			p-id="11002"
			width="22"
			height="22"
		>
			<path
				d="M127.658667 492.885333c0-51.882667 10.24-101.717333 30.378666-149.162666s47.786667-88.064 81.92-122.538667 75.093333-61.781333 122.538667-81.92 96.938667-30.378667 149.162667-30.378667 101.717333 10.24 149.162666 30.378667 88.405333 47.786667 122.88 81.92 61.781333 75.093333 81.92 122.538667 30.378667 96.938667 30.378667 149.162666-10.24 101.717333-30.378667 149.162667-47.786667 88.405333-81.92 122.88-75.093333 61.781333-122.88 81.92-97.28 30.378667-149.162666 30.378667-101.717333-10.24-149.162667-30.378667-88.064-47.786667-122.538667-81.92-61.781333-75.093333-81.92-122.88-30.378667-96.938667-30.378666-149.162667z m329.045333 0c0 130.048 13.994667 244.394667 41.984 343.381334h12.970667c46.762667 0 91.136-9.216 133.461333-27.306667s78.848-42.666667 109.568-73.386667 54.954667-67.242667 73.386667-109.568 27.306667-86.698667 27.306666-133.461333c0-46.421333-9.216-90.794667-27.306666-133.12s-42.666667-78.848-73.386667-109.568-67.242667-54.954667-109.568-73.386667-86.698667-27.306667-133.461333-27.306666h-11.605334c-28.672 123.562667-43.349333 237.909333-43.349333 343.722666z"
				p-id="11003"
			></path>
		</svg>
	);

	const list = allState.filter((state) => state !== prefersColor);

	const changeColor = (ev, toColor) => {
		if (!isSideMenu && onDarkSwitchClick) {
			onDarkSwitchClick(ev);
		}
		if (toColor === prefersColor) return;
		setColor(toColor);
		if (toColor === allState[2]) {
			const darkMode = window.matchMedia('(prefers-color-scheme: dark)');
			document.body.setAttribute('theme-mode', darkMode.matches ? allState[1] : allState[0]);
		} else {
			document.body.setAttribute('theme-mode', toColor);
		}
	};

	// 自动切换颜色
	useEffect(() => {
		const cache = localStorage.getItem('dumi:prefers-color') ?? allState[2];
		const darkMode = window.matchMedia('(prefers-color-scheme: dark)');
		// 监听主题切换事件
		if (cache === allState[2]) {
			document.body.setAttribute('theme-mode', darkMode.matches ? allState[1] : allState[0]);
			darkMode?.addEventListener('change', (e) => {
				document.body.setAttribute('theme-mode', e.matches ? allState[1] : allState[0]);
			});
		} else {
			document.body.setAttribute('theme-mode', cache);
		}
	}, []);

	const getSvg = (baseColor) => {
		switch (baseColor) {
			case 'dark':
				return (
					<button
						key="dumi-dark-btn-moon"
						title="Dark theme"
						onClick={(ev) => changeColor(ev, baseColor)}
						className={`__dumi-default-dark-moon ${
							baseColor === prefersColor ? '__dumi-default-dark-switch-active' : ''
						}`}
					>
						{moonSvg}
					</button>
				);
			case 'light':
				return (
					<button
						key="dumi-dark-btn-sun"
						title="Light theme"
						onClick={(ev) => changeColor(ev, baseColor)}
						className={`__dumi-default-dark-sun ${
							baseColor === prefersColor ? '__dumi-default-dark-switch-active' : ''
						}`}
					>
						{sunSvg}
					</button>
				);
			case 'auto':
				return (
					<button
						key="dumi-dark-btn-auto"
						title="Default to system"
						onClick={(ev) => changeColor(ev, baseColor)}
						className={`__dumi-default-dark-auto ${
							baseColor === prefersColor ? '__dumi-default-dark-switch-active' : ''
						}`}
					>
						{autoSvg}
					</button>
				);
			default:
		}
	};

	return (
		<div className="__dumi-default-dark">
			<div
				className={`__dumi-default-dark-switch ${
					!isSideMenu && darkSwitch ? '__dumi-default-dark-switch-open' : ''
				}`}
			>
				{isSideMenu ? allState.map((item) => getSvg(item)) : getSvg(prefersColor)}
			</div>
			{!isSideMenu && darkSwitch && (
				<div className="__dumi-default-dark-switch-list">{list.map((item) => getSvg(item))}</div>
			)}
		</div>
	);
};

export default Dark;
