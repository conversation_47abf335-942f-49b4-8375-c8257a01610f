@import (reference) '../style/variables.less';

.@{prefix}-menu {
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  bottom: 0;
  width: @s-menu-width;
  background-color: #f2f5fa;
  box-sizing: border-box;
  transition: left 0.3s;

  &[data-hidden] {
    display: none;
  }

  @media @mobile {
    left: -@s-menu-mobile-width;
    top: @s-mobile-nav-height;
    display: block !important;
    width: @s-menu-mobile-width;
    background-color: #fff;

    &[data-mobile-show] {
      left: 0;
    }

    [data-prefers-color=dark] & {
      background-color: @c-bg-dark;
    }
  }

  // shadow
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    display: block;
    width: 20px;
    background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.03));
    pointer-events: none;

    // use border on mobile devices
    @media @mobile {
      width: 1px;
      background: @c-border;
    }
  }

  &-header {
    position: relative;
    padding-top: 40px;
    text-align: center;
    border-bottom: 1px solid @c-border;

    [data-prefers-color=dark] & {
      border-color: @c-border-dark;
    }

    @media @mobile {
      display: none;
    }

    .@{prefix}-menu-logo {
      display: inline-block;
      width: 66px;
      height: 65px;
      background: url(@img-logo) no-repeat 0 / contain;
    }

    h1 {
      margin: 10px 0 0;
      color: @c-heading;
      font-weight: 500;
      line-height: 1.40625;
    }

    p {
      margin: 0 0 5px;
      color: lighten(@c-secondary, 10%);

      // badges
      > object[data^='https://img.shields.io'] {
        max-height: 20px;
      }

      + p {
        margin-bottom: 10px;
      }
    }
  }

  &-doc-locale {
    padding: 16px 0;
    text-align: center;
    border-bottom: 1px solid @c-border;
    display: flex;
    justify-content: space-evenly;

    [data-prefers-color=dark] & {
      border-color: @c-border-dark;
    }

    [data-mode=doc][data-mobile-show=true] & {
      display: grid;
    }

    &:empty {
      display: none;
    }
  }

  &-inner {
    width: 100%;
    height: 100%;
    overflow: auto;
    overscroll-behavior: contain;

    [data-prefers-color=dark] & {
      background-color: #262626;
    }

    // common list styles
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
      font-size: 16px;

      li {
        color: @c-text;
        a,
        > span {
          position: relative;
          display: block;
          padding-right: 24px;
          color: @c-heading;
          line-height: 2.4;
          text-decoration: none;
          outline: none;
          transition: color 0.3s, background 0.3s;

          [data-prefers-color=dark] & {
            color: @c-heading-dark;
          }

          span {
            display: block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          &:hover,
          &.active {
            color: @c-primary;

            [data-prefers-color=dark] & {
              color: @c-primary-dark;
            }
          }

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -10px;
            margin-top: -2.5px;
            display: inline-block;
            width: 5px;
            height: 5px;
            background-color: @c-primary;
            border-radius: 50%;
            opacity: 0;
            transition: transform 0.2s, opacity 0.2s;
            transform: scale(0) translateX(-10px);
          }
        }

        &.active a,
        a.active {
          &::before {
            opacity: 1;
            transform: scale(1) translateX(0);
          }
        }

        // level larger, offset larger, font size smaller
        ul {
          font-size: 0.9em;
          padding-left: 1em;
        }
      }
    }

    // 1-level list styles
    > ul {
      > li > a {
        line-height: 2.875;

        &:not([href]) {
          padding-top: 24px;
          line-height: 1;
          font-weight: 500;
          color: @c-heading !important;
          background: transparent !important;
          cursor: default;

          [data-prefers-color=dark] & {
            color: @c-heading-dark !important;
          }
        }
      }

      > li:first-child > a:not([href]) {
        padding-top: 0;
      }
    }

    // n-level list styles
    > ul ul {
      a {
        color: @c-secondary;
        [data-prefers-color=dark] & {
          color: @c-secondary-dark;
        }
        &.active {
          color: @c-primary;
          [data-prefers-color=dark] & {
            color: @c-primary-dark;
          }
        }
      }
    }

    .@{prefix}-menu-mobile-area {
      display: none;
      padding-bottom: 16px;
      margin-bottom: 16px;
      text-align: center;
      border-bottom: 1px solid @c-border;

      [data-prefers-color=dark] & {
        border-color: @c-border-dark;
      }

      @media @mobile {
        display: block;
      }
    }

    // mobile nav list
    .@{prefix}-menu-nav-list {
      padding: 16px 0 0 0;

      > li,
      > li > a {
        padding-right: 0;
        line-height: 2.4;

        ul {
          padding-left: 0;

          a {
            padding-right: 0;
            font-size: 90%;
          }
        }
      }
    }

    // menu list
    .@{prefix}-menu-list {
      padding: 8px 0;
      margin-bottom: 40px;

      > li > a {
        @c-active-bg: #e8ecf4;

        padding-left: 28px;

        &.active {
          background: linear-gradient(to left, #e8ecf4, rgba(232, 236, 244, 0));

          [data-prefers-color=dark] & {
            background: linear-gradient(to left, #3d3d3e, rgba(255, 255, 255, 0.06));
          }
        }

        ~ ul {
          margin-top: 8px;
          margin-left: 28px;
        }

        @media @mobile {
          padding-left: 16px;

          ~ ul {
            margin-left: 16px;
          }
        }
      }
    }
  }

  &[data-mode='site'] {
    &::after {
      width: 1px;
      background: @c-border;

      [data-prefers-color=dark] & {
        background: @c-border-dark;
      }
    }

    .@{prefix}-menu-list {
      padding: 0;

      > li > a {
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          right: 0;
          display: block;
          width: 3px;
          background-color: @c-primary;
          visibility: hidden;
          opacity: 0;
          transition: all 0.3s;
          border-radius: 1px;
        }

        &.active {
          z-index: 1;
          background: linear-gradient(to left, #f8faff, rgba(248, 250, 255, 0));

          [data-prefers-color=dark] & {
            background: linear-gradient(to left, #3d3d3e, rgba(255, 255, 255, 0.06));
          }
          &::after {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }

    @media @desktop {
      top: @s-nav-height;
      width: @s-site-menu-width;
      padding: 24px 0;
      background: transparent;

      [data-prefers-color=dark] & {
        background: @c-light-bg-dark;
      }

      .@{prefix}-menu-nav,
      .@{prefix}-menu-header {
        display: none;
      }
    }
  }
}
