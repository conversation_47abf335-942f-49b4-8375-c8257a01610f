@import './markdown.less';
@import './variables.less';

@s-toc-width: 136px;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji,
    Segoe UI Symbol;
  font-variant: tabular-nums;
  font-feature-settings: 'tnum';
  transition: background 0.2s cubic-bezier(0.075,0.82,0.165,1), color 0.2s cubic-bezier(0.075,0.82,0.165,1);

  [data-prefers-color=dark] & {
    color: @c-text-dark;
    background-color: @c-bg-dark;
  }
}

.@{prefix}-layout {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 16px (@s-content-margin + @s-toc-width) 50px @s-menu-width + @s-content-margin;

  @media @mobile {
    padding-top: 66px !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
  }

  &[data-gapless='true'] {
    padding-top: @s-nav-height !important;
    padding-right: 0 !important;
    padding-left: 0 !important;
    padding-bottom: 0;

    @media @mobile {
      padding-top: @s-mobile-nav-height !important;
    }
  }

  &[data-show-sidemenu='false'] {
    padding-left: @s-content-margin;
  }

  &[data-show-slugs='false'] {
    padding-right: @s-content-margin;
  }

  &[data-site-mode='true'] {
    padding-top: @s-nav-height + 50px;

    &[data-show-sidemenu='true'] {
      padding-left: @s-site-menu-width + 50px;
    }

    &[data-show-slugs='true'] {
      padding-right: @s-content-margin + @s-toc-width + 14;
    }

    .@{prefix}-layout-content > .markdown:first-child > *:first-child {
      margin-top: 0;
    }

    .@{prefix}-layout-toc {
      top: 114px;
      max-height: calc(90vh - 144px);
    }
  }

  &-hero {
    margin: -50px -58px 0;
    padding: 100px 0;
    text-align: center;
    background-color: #f5f6f8;

    [data-prefers-color=dark] & {
      background-color: @c-light-bg-dark;
    }

    @media @mobile {
      margin: -16px -16px 0;
      padding: 48px 0;
    }

    img {
      max-width: 100%;
      max-height: 200px;
      margin-bottom: 1rem;
    }

    h1 {
      margin: 0 0 16px;
      font-size: 48px;
      font-weight: 600;
      line-height: 56px;
      color: #080e29;

      [data-prefers-color=dark] & {
        color: @c-heading-dark;
      }

      + div {
        margin: 16px 0 32px;
        opacity: 0.78;

        .markdown {
          font-size: 16px;
        }
      }
    }

    button {
      margin-right: 16px;
      padding: 0 32px;
      height: 44px;
      color: @c-primary;
      font-size: 16px;
      background: transparent;
      border: 1px solid @c-primary;
      border-radius: 22px;
      box-sizing: border-box;
      cursor: pointer;
      outline: none;
      transition: all 0.3s;

      &:hover {
        opacity: 0.8;
      }

      &:active {
        opacity: 0.9;
      }
    }

    a:last-child button {
      margin-right: 0;
      color: #fff;
      background: @c-primary;
    }
  }

  &-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 96px;
    grid-row-gap: 56px;
    padding: 72px 0;

    > dl {
      flex: 1;
      margin: 0;
      text-align: center;
      background: no-repeat center top / auto 48px;

      &[style*='background-image'] {
        padding-top: 64px;
      }

      dt {
        margin-bottom: 12px;
        font-size: 20px;
        line-height: 1;
        color: @c-heading;
        [data-prefers-color=dark] & {
          color: @c-text-dark;
        }
      }

      a {
        transition-duration: none;
      }

      a dt {
        color: @c-link;
        transition: opacity 0.2s;
        &:hover {
          opacity: 0.7;
          text-decoration: underline;
        }

        &:active {
          opacity: 0.9;
        }
      }

      dd {
        margin: 0;

        .markdown {
          color: @c-secondary;
          font-size: 14px;
          line-height: 22px;

          [data-prefers-color=dark] & {
            color: @c-secondary-dark;
          }

          > p:first-child {
            margin-top: 0;
          }

          > p:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    @media @mobile {
      display: block;
      padding: 40px 0;

      > dl {
        text-align: left;
        background-position: left top;

        &[style*='background-image'] {
          padding: 0 0 0 60px;
        }

        + dl {
          margin-top: 32px;
        }
      }
    }
  }

  &-features,
  &-features + &-content,
  &-hero + &-content {
    margin-left: auto;
    margin-right: auto;
    max-width: 960px;
  }

  &-hero + &-content {
    margin-top: 60px;
  }

  &-toc {
    list-style: none;
    position: fixed;
    z-index: 10;
    top: 50px;
    right: 0;
    width: @s-toc-width;
    max-height: calc(90vh - 80px);
    margin: 0;
    padding: 0 24px 0 0;
    background-color: #fff;
    box-sizing: content-box;
    overflow: auto;

    [data-prefers-color=dark] & {
      background-color: @c-bg-dark;
    }

    @media @mobile {
      display: none;
    }

    li {
      position: relative;
      margin: 0;
      padding: 4px 0 4px 6px;
      text-indent: 12px;
      font-size: 13px;
      line-height: 1.40625;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;

      a {
        color: @c-text;
        text-decoration: none;

        [data-prefers-color=dark] & {
          color: @c-text-dark;
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          display: inline-block;
          width: 2px;
          background: @c-border;
        }

        &:hover {
          color: lighten(@c-primary, 5%);

          [data-prefers-color=dark] & {
            color: lighten(@c-primary-dark, 5%);
          }
        }

        &:active {
          color: lighten(@c-primary, 3%);

          [data-prefers-color=dark] & {
            color: lighten(@c-primary-dark, 3%);
          }
        }

        &.active {
          color: @c-primary;

          [data-prefers-color=dark] & {
            color: @c-primary-dark;
          }

          &::before {
            background: @c-primary;

            [data-prefers-color=dark] & {
              background: @c-primary-dark;
            }
          }
        }
      }
    }
  }

  &-footer-meta {
    margin-top: 40px;
    padding-top: 24px;
    display: flex;
    color: @c-secondary;
    font-size: 14px;
    justify-content: space-between;
    border-top: 1px solid @c-border;

    [data-prefers-color=dark] & {
      color: @c-secondary-dark;
      border-color: @c-border-dark;
    }

    @media only screen and (max-width: 960px) {
      display: block;
    }

    > a {
      margin-bottom: 4px;
      display: block;
      color: @c-primary;
      transition: opacity 0.2s;
      text-decoration: none;

      [data-prefers-color=dark] & {
        color: @c-primary-dark;
      }

      &:hover {
        opacity: 0.7;
        text-decoration: underline;
      }

      &:active {
        opacity: 0.9;
      }
    }

    > span:last-child {
      &::before {
        content: attr(data-updated-text);
        color: @c-primary;

        [data-prefers-color=dark] & {
          color: @c-primary-dark;
        }
      }
    }
  }
}

.__dumi-default-layout-footer {
  margin: 72px 0 -32px;
  padding-top: 24px;
  border-top: 1px solid @c-border;
  text-align: center;

  [data-prefers-color=dark] & {
    border-color: @c-border-dark;
  }

  .markdown {
    color: #b0b1ba;

    [data-prefers-color=dark] & {
      color: rgba(255,255,255,0.45);
    }
  }
}
