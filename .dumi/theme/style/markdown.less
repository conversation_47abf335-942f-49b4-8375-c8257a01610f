@import (reference) './variables.less';

.markdown {
  color: @c-text;
  font-size: 15px;
  line-height: 1.60625;

  [data-prefers-color=dark] & {
    color: @c-secondary-dark;
  }

  &:not(:first-child):empty {
    min-height: 32px;
  }

  // titles
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 42px 0 18px;
    color: @c-heading;
    font-weight: 500;
    line-height: 1.40625;

    [data-prefers-color=dark] & {
      color: @c-heading-dark;
    }

    // anchor link
    &:hover > a[aria-hidden] {
      float: left;
      margin-top: 0.06em;
      margin-left: -20px;
      width: 20px;
      padding-right: 4px;
      line-height: 1;
      box-sizing: border-box;

      @media @mobile {
        width: 14px;
        margin-left: -14px;
      }

      &::after {
        content: '#';
        display: inline-block;
        vertical-align: middle;
        font-size: 20px;
      }

      span {
        display: none;
      }
    }

    + h1,
    + h2,
    + h3,
    + h4,
    + h5,
    + h6 {
      margin-top: 16px;
    }
  }

  h1 {
    margin-top: 48px;
    margin-bottom: 32px;
    font-size: 32px;
  }

  h2 {
    font-size: 24px;
  }

  h3 {
    font-size: 20px;
  }

  h4 {
    font-size: 18px;
  }

  h5 {
    font-size: 16px;
  }

  h6 {
    font-size: 14px;
  }

  // paragraph
  p {
    margin: 16px 0;
  }

  // inline code
  *:not(pre) code {
    padding: 2px 5px;
    color: #d56161;
    background: darken(@c-light-bg, 1%);

    [data-prefers-color=dark] & {
      color: #ff7875;
      background: @c-light-bg-dark;
    }
  }

  code {
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  }

  // code block
  pre {
    font-size: 14px;
    background: darken(@c-light-bg, 1%);

    &:not([class^='language-']) {
      padding: 1em;
    }
  }

  // horizontal line
  hr {
    margin: 16px 0;
    border: 0;
    border-top: 1px solid @c-border;
  }

  // blockquote
  blockquote {
    margin: 16px 0;
    padding: 0 24px;
    color: fadeout(@c-text, 30%);
    border-left: 4px solid @c-border;
    overflow: hidden;

    [data-prefers-color=dark] & {
      color: fadeout(@c-text-dark, 30%);
      border-color: @c-border-dark;
    }
  }

  // list
  ul,
  ol {
    margin: 8px 0 8px 32px;
    padding: 0;

    li {
      margin-bottom: 4px;
    }
  }

  // table
  table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid @c-border;

    [data-prefers-color=dark] & {
      border: 1px solid #3b434b;
    }

    th,
    td {
      padding: 10px 24px;
      border: 1px solid @c-border;

      [data-prefers-color=dark] & {
        border: 1px solid #3b434b;
      }
    }

    th {
      font-weight: 600;
      background: @c-light-bg;

      [data-prefers-color=dark] & {
        background: @c-light-bg-dark;
      }
    }

    td:first-child {
      font-weight: 500;
    }

    a {
      svg {
        display: none;
      }
    }
  }

  // links
  a {
    color: @c-link;
    text-decoration: none;
    transition: opacity 0.2s;
    outline: none;

    [data-prefers-color=dark] & {
      color: @c-primary-dark;
    }

    &:hover {
      opacity: 0.7;
      text-decoration: underline;
    }

    &:active {
      opacity: 0.9;
    }
  }

  // images
  img {
    max-width: 100%;
  }
}

.@{prefix} {
  &-external-link-icon {
    vertical-align: -0.155em;
    margin-left: 2px;
  }
}

// For demo
[data-prefers-color=dark] {
  h1,
  h2,
  h3,
  h4 {
    color: @c-heading-dark;
  }
}
