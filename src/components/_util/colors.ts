import type { ElementOf } from './type';
import { tuple } from './type';

export const PresetStatusColorTypes = tuple('success', 'processing', 'error', 'default', 'warning');
// eslint-disable-next-line import/prefer-default-export
export const PresetColorTypes = tuple(
  'pink',
  'red',
  'yellow',
  'orange',
  'cyan',
  'green',
  'blue',
  'purple',
  'geekblue',
  'magenta',
  'volcano',
  'gold',
  'lime',
);

export type PresetColorType = ElementOf<typeof PresetColorTypes>;
export type PresetStatusColorType = ElementOf<typeof PresetStatusColorTypes>;

// Color generation algorithm based on HSV color space
const hueStep = 2;
const saturationStep = 0.16;
const saturationStep2 = 0.05;
const brightnessStep1 = 0.05;
const brightnessStep2 = 0.15;
const lightColorCount = 5;
const darkColorCount = 4;

const getHue = (hsv: { h: number; s: number; v: number }, i: number, isLight: boolean) => {
  let hue: number;
  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {
    hue = isLight ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;
  } else {
    hue = isLight ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;
  }
  if (hue < 0) {
    hue += 360;
  } else if (hue >= 360) {
    hue -= 360;
  }
  return hue;
};

const getSaturation = (hsv: { h: number; s: number; v: number }, i: number, isLight: boolean) => {
  if (hsv.h === 0 && hsv.s === 0) {
    return hsv.s;
  }
  let saturation: number;
  if (isLight) {
    saturation = hsv.s - saturationStep * i;
  } else if (i === darkColorCount) {
    saturation = hsv.s + saturationStep;
  } else {
    saturation = hsv.s + saturationStep2 * i;
  }
  if (saturation > 1) {
    saturation = 1;
  }
  if (isLight && i === lightColorCount && saturation > 0.1) {
    saturation = 0.1;
  }
  if (saturation < 0.06) {
    saturation = 0.06;
  }
  return saturation;
};

const getValue = (hsv: { h: number; s: number; v: number }, i: number, isLight: boolean) => {
  let value: number;
  if (isLight) {
    value = hsv.v + brightnessStep1 * i;
  } else {
    value = hsv.v - brightnessStep2 * i;
  }
  if (value > 1) {
    value = 1;
  }
  return value < 0.01 ? 0.01 : value;
};

// Convert HSV to RGB
const hsvToRgb = (h: number, s: number, v: number) => {
  const c = v * s;
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
  const m = v - c;
  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 60) {
    r = c; g = x;
  } else if (60 <= h && h < 120) {
    r = x; g = c;
  } else if (120 <= h && h < 180) {
    g = c; b = x;
  } else if (180 <= h && h < 240) {
    g = x; b = c;
  } else if (240 <= h && h < 300) {
    r = x; b = c;
  } else if (300 <= h && h < 360) {
    r = c; b = x;
  }

  return {
    r: Math.round((r + m) * 255),
    g: Math.round((g + m) * 255),
    b: Math.round((b + m) * 255),
  };
};

// Convert RGB to HSV
const rgbToHsv = (r: number, g: number, b: number) => {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;

  let h = 0;
  if (diff !== 0) {
    if (max === r) {
      h = ((g - b) / diff) % 6;
    } else if (max === g) {
      h = (b - r) / diff + 2;
    } else {
      h = (r - g) / diff + 4;
    }
  }
  h = Math.round(h * 60);
  if (h < 0) h += 360;

  const s = max === 0 ? 0 : diff / max;
  const v = max;

  return { h, s, v };
};

// Convert RGB to HEX
const rgbToHex = (r: number, g: number, b: number) => {
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
};

// Parse color string to RGB
const parseColor = (color: string) => {
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return { r, g, b };
  }
  // Add more color format parsing if needed
  throw new Error(`Unsupported color format: ${color}`);
};

/**
 * Generate color palette based on primary color
 * @param color Primary color in hex format
 * @returns Array of 10 colors from light to dark
 */
export const generate = (color: string): string[] => {
  const { r, g, b } = parseColor(color);
  const hsv = rgbToHsv(r, g, b);

  const colors: string[] = [];

  // Generate light colors (1-5)
  for (let i = lightColorCount; i > 0; i--) {
    const h = getHue(hsv, i, true);
    const s = getSaturation(hsv, i, true);
    const v = getValue(hsv, i, true);
    const rgb = hsvToRgb(h, s, v);
    colors.push(rgbToHex(rgb.r, rgb.g, rgb.b));
  }

  // Add primary color (6)
  colors.push(color);

  // Generate dark colors (7-10)
  for (let i = 1; i <= darkColorCount; i++) {
    const h = getHue(hsv, i, false);
    const s = getSaturation(hsv, i, false);
    const v = getValue(hsv, i, false);
    const rgb = hsvToRgb(h, s, v);
    colors.push(rgbToHex(rgb.r, rgb.g, rgb.b));
  }

  return colors;
};

// Preset colors
export const red = generate('#f5222d');
export const volcano = generate('#fa541c');
export const orange = generate('#fa8c16');
export const gold = generate('#faad14');
export const yellow = generate('#fadb14');
export const lime = generate('#a0d911');
export const green = generate('#52c41a');
export const cyan = generate('#13c2c2');
export const blue = generate('#1890ff');
export const geekblue = generate('#2f54eb');
export const purple = generate('#722ed1');
export const magenta = generate('#eb2f96');
export const pink = generate('#eb2f96');

// Preset palettes object
export const presetPalettes = {
  red,
  volcano,
  orange,
  gold,
  yellow,
  lime,
  green,
  cyan,
  blue,
  geekblue,
  purple,
  magenta,
  pink,
};
