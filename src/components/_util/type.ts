// https://stackoverflow.com/questions/46176165/ways-to-get-string-literal-type-of-array-values-without-enum-overhead
export const tuple = <T extends string[]>(...args: T) => args;

export const tupleNum = <T extends number[]>(...args: T) => args;

/**
 * https://stackoverflow.com/a/59187769 Extract the type of an element of an array/tuple without
 * performing indexing
 */
export type ElementOf<T> = T extends (infer E)[] ? E : T extends readonly (infer F)[] ? F : never;

/** https://github.com/Microsoft/TypeScript/issues/29729 */
export type LiteralUnion<T extends U, U> = T | (U & {});

/**
 * Get property type from object type
 */
export type GetProp<T, K extends keyof T> = T[K];

/**
 * Get ref type from component props
 */
export type GetRef<T> = T extends React.ComponentType<infer P> 
  ? P extends { ref?: React.Ref<infer R> } 
    ? R 
    : never 
  : never;

/**
 * Get context property type
 */
export type GetContextProp<T, K extends keyof T> = T[K];
