---
order: 5
title:
    zh-CN: 设置锚点滚动偏移量
    en-US: Set Anchor scroll offset
---

锚点目标滚动到屏幕正中间。

```tsx | preview
import { Anchor } from '@hose/eui';
import React, { useEffect, useState } from 'react';

const { Link } = Anchor;

const App: React.FC = () => {
	const [targetOffset, setTargetOffset] = useState<number | undefined>(undefined);

	useEffect(() => {
		setTargetOffset(window.innerHeight / 2);
	}, []);

	return (
		<Anchor affix={false} targetOffset={targetOffset}>
			<Link href="#基本" title="基础" />
			<Link href="#静态位置" title="静态位置" />
			<Link href="#api" title="API">
				<Link href="#anchor-props" title="Anchor Props" />
				<Link href="#link-props" title="Link Props" />
			</Link>
		</Anchor>
	);
};

export default App;
```
