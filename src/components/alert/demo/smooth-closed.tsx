import React, { useState } from 'react';
import { Alert, Space } from '@hose/eui';

const App: React.FC = () => {
  const [visible, setVisible] = useState(true);

  const handleClose = () => {
    setVisible(false);
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      {visible && (
        <Alert message="这是一条提示文本信息" type="success" closable afterClose={handleClose} />
      )}
      <p>点击关闭按钮查看效果</p>
      {/* <Switch onChange={setVisible} checked={visible} disabled={visible} /> */}
    </Space>
  );
};

export default App;
