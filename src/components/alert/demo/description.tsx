import React from 'react';
import { Alert, Space } from '@hose/eui';

const App: React.FC = () => (
	<Space direction="vertical" style={{ width: '100%' }}>
		<Alert
			type="info"
			message="标题"
			closable
			className='overflow-description'
			description="这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。"
		/>
		<Alert
			message="标题"
			showIcon={false}
			className='overflow-description'
			description="这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。这是正文提示文本信息，文本信息过长建议增加标题，文本行数建议 4 行以内，过长情况下不易阅读，标题宽度与正文宽度等宽处理。"
		/>
	</Space>
);

export default App;
