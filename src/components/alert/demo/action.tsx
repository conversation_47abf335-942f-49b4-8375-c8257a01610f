import React from 'react';
import { Al<PERSON>, But<PERSON>, Space } from '@hose/eui';

const App: React.FC = () => (
	<Space direction="vertical" style={{ width: '100%' }}>
		<Alert
			message="这是一条提示文本信息"
			type="success"
			showIcon={false}
			action={
				<Button size="small" category="text" theme="highlight">
					文字按钮
				</Button>
			}
		/>
		<Alert
			message="这是一条提示文本信息这是一条提示文本信息这是一条提示文本信息这是一条提示文本信息"
			type="success"
			showIcon={false}
			action={
				<Button size="small" category="text" theme="highlight">
					文字按钮
				</Button>
			}
		/>
		<Alert
			message="这是一条提示文本信息"
			type="success"
			showIcon
			action={
				<Button size="small" category="text" theme="highlight">
					文字按钮
				</Button>
			}
		/>
		<Alert
			message="这是一条提示文本信息"
			type="success"
			showIcon
			action={
				<Button size="small" category="text" theme="highlight">
					文字按钮
				</Button>
			}
			closable
		/>
		<Alert
			message="这是一条提示文本信息"
			type="success"
			showIcon
			action={
				<Space direction="horizontal">
					<Button size="small" category="text" theme="highlight">
						文字按钮
					</Button>
					<Button size="small" category="text" theme="highlight">
						文字按钮
					</Button>
				</Space>
			}
			closable
		/>
		<Alert
			message="这是一条提示文本信息"
			showIcon
			description="这是一条提示文本信息这是一条提示文本信息这是一条提示文本信息这是一条提示文本信息"
			type="success"
			action={
				<Button size="small" category="text" theme="highlight">
					文字按钮
				</Button>
			}
		/>
		<Alert
			message="这是一条提示文本信息"
			description="这是一条提示文本信息这是一条提示文本信息这是一条提示文本信息这是一条提示文本信息"
			type="success"
			action={
				<Space direction="horizontal">
					<Button size="small" category="text" theme="highlight">
						文字按钮
					</Button>
					<Button size="small" category="text" theme="highlight">
						文字按钮
					</Button>
				</Space>
			}
			closable
		/>
	</Space>
);

export default App;
