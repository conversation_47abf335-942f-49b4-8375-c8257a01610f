import React from 'react';
import { OutlinedGeneralEmoji } from '@hose/eui-icons';
import { Alert, Space } from '@hose/eui';

const icon = <OutlinedGeneralEmoji />;

const App: React.FC = () => (
	<Space direction="vertical" style={{ width: '100%' }}>
		<Alert icon={icon} message="showIcon = false" type="success" showIcon={false} />
		<Alert icon={icon} message="Success Tips" type="success" />
		<Alert icon={icon} message="Informational Notes" type="info" />
		<Alert icon={icon} message="Warning" type="warning" />
		<Alert icon={icon} message="Error" type="error" />
		<Alert
			icon={icon}
			message="Success Tips"
			description="Detailed description and advices about successful copywriting."
			type="success"
		/>
		<Alert
			icon={icon}
			message="Informational Notes"
			description="Additional description and informations about copywriting."
			type="info"
		/>
		<Alert icon={icon} message="Warning" description="This is a warning notice about copywriting." type="warning" />
		<Alert icon={icon} message="Error" description="This is an error message about copywriting." type="error" />
	</Space>
);

export default App;
