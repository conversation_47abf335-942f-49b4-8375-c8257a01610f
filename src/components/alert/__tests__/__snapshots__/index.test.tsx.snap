// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Alert custom action 1`] = `
<div
  class="eui-alert eui-alert-success"
  data-show="true"
  role="alert"
>
  <span
    aria-label="FilledTipsYes"
    class="eui-icon eui-icon-FilledTipsYes eui-alert-icon"
    role="img"
  >
    <svg
      aria-hidden="true"
      fill="none"
      focusable="false"
      height="1em"
      viewBox="0 0 48 48"
      width="1em"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2 24C2 36.15 11.85 46 24 46C36.15 46 46 36.15 46 24C46 11.85 36.15 2 24 2C11.85 2 2 11.85 2 24ZM31.5889 18.6512C32.3635 17.8765 33.623 17.8964 34.3974 18.6712C35.1718 19.446 35.1915 20.7056 34.4167 21.48C30.6429 25.2519 26.8658 29.0208 23.097 32.7978C22.315 33.5816 21.045 33.5816 20.2628 32.798C18.2934 30.8253 16.321 28.8557 14.3495 26.8852C13.5759 26.112 13.5989 24.8538 14.3719 24.08C15.1449 23.3061 16.403 23.283 17.1764 24.0564L21.68 28.5601L31.5889 18.6512Z"
        fill="currentColor"
      />
    </svg>
  </span>
  <div
    class="eui-alert-content"
  >
    <div
      class="eui-alert-message"
    >
      Success Tips
    </div>
  </div>
  <div
    class="eui-alert-action"
  >
    <button
      class="eui-button eui-button-text-default eui-button-small eui-button-shape-default"
      type="button"
    >
      UNDO
    </button>
  </div>
  <button
    class="eui-alert-close-icon"
    tabindex="0"
    type="button"
  >
    <span
      aria-label="OutlinedTipsClose"
      class="eui-icon eui-icon-OutlinedTipsClose"
      role="img"
    >
      <svg
        aria-hidden="true"
        fill="none"
        focusable="false"
        height="1em"
        viewBox="0 0 48 48"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M40.5128 40.5123C41.2939 39.7312 41.2995 38.4705 40.5185 37.6895L26.8289 23.9999L40.5184 10.3104C41.2995 9.52938 41.2938 8.26871 40.5128 7.48766C39.7317 6.70662 38.471 6.70096 37.69 7.48201C36.9089 8.26306 24.0005 21.1715 24.0005 21.1715L10.3109 7.48189C9.52986 6.70084 8.26919 6.7065 7.48814 7.48755C6.70709 8.2686 6.70143 9.52927 7.48247 10.3103L21.1721 23.9999L7.48238 37.6896C6.70133 38.4707 6.70699 39.7313 7.48804 40.5124C8.26909 41.2934 9.52976 41.2991 10.3108 40.518L24.0005 26.8283L37.6901 40.5179C38.4711 41.299 39.7318 41.2933 40.5128 40.5123Z"
          fill="currentColor"
        />
      </svg>
    </span>
  </button>
</div>
`;

exports[`Alert rtl render component should be rendered correctly in RTL direction 1`] = `
<div
  class="eui-alert eui-alert-info eui-alert-no-icon eui-alert-rtl"
  data-show="true"
  role="alert"
>
  <div
    class="eui-alert-content"
  />
</div>
`;
