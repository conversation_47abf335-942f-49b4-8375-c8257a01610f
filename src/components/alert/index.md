# Alert 警告提示 ✅

警告提示，展现需要关注的信息。

## 何时使用

- 当某个页面需要向用户显示警告的信息时。
- 非浮层的静态展现形式，始终展现，不会自动消失，用户可以点击关闭。

## 代码演示

<!-- prettier-ignore -->
### 基本
##### 最简单的用法，适用于简短的警告提示。
<code src="./demo/basic.tsx"></code>
### 四种样式
##### 共有四种样式`success`、`info`、`warning`、`error`。
<code src="./demo/style.tsx"></code>
### 自定义关闭
##### 可以自定义关闭，自定义的文字或组件会替换原先的关闭 Icon。
<code src="./demo/close-text.tsx"></code>
### 操作
##### 可以添加自定义操作项
<code src="./demo/action.tsx"></code>
### 含有辅助性文字介绍
##### 含有辅助性文字介绍的警告提示。
<code src="./demo/description.tsx"></code>
### 顶部公告
##### 页面顶部通告形式，默认有图标且 `type` 为 'warning'。
<code src="./demo/banner.tsx" iframe="250"></code>
### 轮播的公告
##### 配合 [react-text-loop-next](https://www.npmjs.com/package/react-text-loop-next) 或 [react-fast-marquee](https://www.npmjs.com/package/react-fast-marquee) 实现消息轮播通知栏。
<code src="./demo/loop-banner.tsx"></code>
### 平滑地卸载
##### 平滑、自然的卸载提示。
<code src="./demo/smooth-closed.tsx"></code>
### React 错误处理
<code src="./demo/error-boundary.tsx"></code>
### 自定义图标
<code src="./demo/custom-icon.tsx" debug></code>

<style>
  .overflow-description .eui-alert-description {
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical
  }

  pre {
    overflow-x: auto;
  }
</style>

## API

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| action | 自定义操作项 | ReactNode | - |
| afterClose | 关闭动画结束后触发的回调函数 | () => void | - |
| banner | 是否用作顶部公告 | boolean | false |
| closable | 默认不显示关闭按钮 | boolean | - |
| closeText | 自定义关闭按钮 | ReactNode | - |
| closeIcon | 自定义关闭 Icon | ReactNode | `<OutlinedTipsClose />` |
| description | 警告提示的辅助性文字介绍 | ReactNode | - |
| icon | 自定义图标，`showIcon` 为 true 时有效 | ReactNode | - |
| message | 警告提示内容 | ReactNode | - |
| showIcon | 是否显示辅助图标 | boolean | true |
| type | 指定警告提示的样式，有四种选择 `success`、`info`、`warning`、`error` | string | `info`，`banner` 模式下默认值为 `warning` |
| onClose | 关闭时触发的回调函数 | (e: MouseEvent) => void | - |

### Alert.ErrorBoundary

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| description | 自定义错误内容，如果未指定会展示报错堆栈 | ReactNode | {{ error stack }} |
| message | 自定义错误标题，如果未指定会展示原生报错信息 | ReactNode | {{ error }} |
