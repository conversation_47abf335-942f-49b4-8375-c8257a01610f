@import '../../style/themes/index';
@import '../../style/mixins/index';

@alert-prefix-cls: ~'@{eui-prefix}-alert';
@icon-font-size: 16px;

.@{alert-prefix-cls} {
	position: relative;
	padding: @alert-padding-vertical @alert-padding-horizontal;
	font: var(--eui-font-body-r1);
	color: var(--eui-text-title);
	word-wrap: break-word;
	border-radius: 6px;

	&-message-wrap {
		display: flex;
		align-items: center;
	}

	&-message {
		flex: 1;
	}

	&-icon {
		margin-right: @margin-xs;
		font-size: @icon-font-size;
	}

	&-description {
		display: none;
	}

	&-success {
		background-color: var(--eui-function-success-50);
		.@{alert-prefix-cls}-icon {
			color: var(--eui-function-success-500);
		}
	}

	&-info {
		background-color: var(--eui-function-info-50);
		.@{alert-prefix-cls}-icon {
			color: var(--eui-function-info-500);
		}
	}

	&-warning {
		background-color: var(--eui-function-warning-50);
		.@{alert-prefix-cls}-icon {
			color: var(--eui-function-warning-500);
		}
	}

	&-error {
		background-color: var(--eui-function-danger-50);
		.@{alert-prefix-cls}-icon {
			color: var(--eui-function-danger-500);
		}

		.@{alert-prefix-cls}-description > pre {
			margin: 0;
			padding: 0;
		}
	}

	&-action {
		margin-left: @margin-xs;
	}

	&-close-icon {
		margin-left: @margin-xs;
		padding: 0;
		overflow: hidden;
		font-size: @icon-font-size;
		line-height: @icon-font-size;
		background-color: transparent;
		border: none;
		outline: none;
		cursor: pointer;

		.eui-icon {
			display: block;
			color: var(--eui-icon-n1);
			transition: color 0.3s;
		}
	}

	&-close-text {
		color: var(--eui-icon-n1);
    font: var(--eui-font-body-r1);
		transition: color 0.3s;

		&:hover {
			color: var(--eui-icon-n1);
		}
	}

	&-with-description {
		align-items: flex-start;
	}

	&-with-description &-message {
		display: block;
    font: var(--eui-font-body-b1);
	}

	&-with-description &-description {
		margin-top: 6px;
		display: block;
    padding-left: 24px;
	}

  &-with-description &-action {
    margin-top: 4px; 
    margin-left: 24px;
  }

  &-no-icon &-description {
    padding-left: 0;
  }

  &-no-icon &-action {
    margin-left: 0;
  }

	&&-motion-leave {
		overflow: hidden;
		opacity: 1;
		transition: max-height 0.3s @ease-in-out-circ, opacity 0.3s @ease-in-out-circ,
			padding-top 0.3s @ease-in-out-circ, padding-bottom 0.3s @ease-in-out-circ,
			margin-bottom 0.3s @ease-in-out-circ;
	}

	&&-motion-leave-active {
		max-height: 0;
		margin-bottom: 0 !important;
		padding-top: 0;
		padding-bottom: 0;
		opacity: 0;
	}

	&-banner {
		margin-bottom: 0;
	}
}

@import './rtl';
