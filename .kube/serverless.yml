apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  # knative域名会取这个名称
  name: eui
  namespace: serverless
spec:
  template:
    metadata:
      # 目前没发现特别的作用，建议上面的名称加语言即可例如：xx-java
      name: eui-web
      annotations:
        # 自伸缩配置，如果需要服务始终服务需要配置最小1个
        # 开发环境建议最小0个，节省运维成本
        autoscaling.knative.dev/maxScale: '2'
        autoscaling.knative.dev/minScale: '1'
    spec:
      containerConcurrency: 0
      containers:
            # 镜像地址，在 CI 中构建的镜像地址
        - image: >-
            ekb-repo.tencentcloudcr.com/ekb/eui:xxx
          name: user-container
          ports:
            # 端口, 服务启动端口，容器80会自动映射到这个端口
            - containerPort: 80
              name: http1
          readinessProbe:
            successThreshold: 1
            tcpSocket:
              port: 0
          resources: {}
      timeoutSeconds: 300
      imagePullSecrets:
        - name: hose-registry-dev
        - name: tengxun-repo
  traffic:
    - latestRevision: true
      percent: 100
