server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;

    location / {
        add_header Access-Control-Allow-Origin *;
        root   /usr/share/nginx/html;
        try_files $uri /index.html;
    }

    location /npm/ {
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto $scheme;
        proxy_set_header   X-Forwarded-Host npm.ekuaibao.com;
        proxy_ssl_server_name on;
        proxy_ssl_name npm.ekuaibao.com;
        proxy_pass https://npm.ekuaibao.com/-/verdaccio/data/sidebar/;
    }

    location = /business {
        add_header Access-Control-Allow-Origin *;
        js_content business.getBusinessHtml;
        js_fetch_trusted_certificate /etc/nginx/njs/ISRG_Root_X1.pem;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}