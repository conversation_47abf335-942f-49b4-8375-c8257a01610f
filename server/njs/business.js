async function getBusinessHtml(r) {
	const reply = await ngx.fetch('https://npm.ekuaibao.com/-/verdaccio/data/sidebar/@hose/eui-business');
	const json = await reply.json();
	const latest = json['dist-tags'].latest;
	const res = await ngx.fetch(`http://static.ekuaibao.com/eui/eui-business/${latest}/index.html`);
	const html = await res.text();
	r.headersOut['Content-Type'] = 'text/html; charset=utf-8';
	r.return(200, html);
}

export default { getBusinessHtml };
