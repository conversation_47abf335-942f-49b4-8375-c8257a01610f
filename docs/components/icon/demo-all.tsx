import React, { useEffect, useState } from 'react';

import './demo-all.less';

export default () => {
	const [iconUrl, setIconUrl] = useState('');
	const [height, setHeight] = useState(0);

	// icon-iframe 通信事件
	useEffect(() => {
		const handler = (event: MessageEvent) => {
			const data = event.data || {};
			if (data.source === 'icon-iframe') {
				switch (data.type) {
					case 'height':
						setHeight(data.height);
						break;
					case 'copy':
						navigator.clipboard?.writeText(data.text);
						setTimeout(() => {
							alert(`${data.text} 复制成功`);
						},100);
						break;
				}
			}
		};
		window.addEventListener('message', handler);
		return () => {
			window.removeEventListener('message', handler);
		};
	}, []);

	useEffect(() => {
		fetch('https://eui.serverless.ekuaibao.com/npm/@hose/eui-icons')
			.then((res) => res.json())
			.then((res) => {
				setIconUrl(`https://statics.ekuaibao.com/eui/eui-icons/${res.latest.version}/index.html`);
			});
	});

	if (!iconUrl) {
		return <div>加载中...</div>;
	}

	return <iframe src={iconUrl} className="icon-iframe" height={height} />;
};
