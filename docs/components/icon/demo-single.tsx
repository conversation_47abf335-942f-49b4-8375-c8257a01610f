import React from 'react';
import { DemoBlock } from 'demos';
import { Space } from '@hose/eui';
import {
	FilledGeneralThumbsup,
	OutlinedGeneralLike,
	OutlinedGeneralLoading,
	OutlinedDirectionReplace,
	OutlinedGeneralCamera
} from '@hose/eui-icons';

export default () => {
	return (
		<>
			<DemoBlock title="基础用法">
				<Space wrap style={{ fontSize: 36 }}>
					<FilledGeneralThumbsup />
					<OutlinedGeneralLike />
				</Space>
			</DemoBlock>
			<DemoBlock title="大小">
				<Space wrap align="center">
					<OutlinedGeneralCamera fontSize={12} />
					<OutlinedGeneralCamera fontSize={24} />
					<OutlinedGeneralCamera fontSize={36} />
					<OutlinedGeneralCamera fontSize={48} />
				</Space>
			</DemoBlock>
			<DemoBlock title="旋转">
				<Space wrap style={{ fontSize: 36 }}>
					<OutlinedGeneralLoading spin />
					<OutlinedDirectionReplace rotate={30} />
				</Space>
			</DemoBlock>
		</>
	);
};
