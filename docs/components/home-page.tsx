import React, { useContext } from 'react';
import styles from './home-page.less';
import { context } from 'dumi/theme';

export default () => {
	const { locale } = useContext(context);

	function trans<T>(en: T, zh: T) {
		return locale === 'zh' ? zh : en;
	}

	const characteristics = [
		{
			img: 'https://gw.alipayobjects.com/zos/bmw-prod/dd5520d8-44b4-43a6-88ee-c970e3757d39.svg',
			title: trans('Fast', '高性能'),
			txt: trans(
				'No configuration required for optimal package size and performance.',
				'无需配置，即可拥有最佳的包体积大小和最优的性能'
			)
		},
		{
			img: 'https://gw.alipayobjects.com/zos/bmw-prod/33cb2ea7-3025-439a-9ce1-212aae26b1cc.svg',
			title: trans('Customizable', '可定制'),
			txt: trans(
				'Based on CSS variables, you can flexibly adjust the appearance of components or freely create your own themes.',
				'基于 CSS 变量，可以灵活地调整组件外观或自由创造自己的主题'
			)
		},
		{
			img: 'https://gw.alipayobjects.com/zos/bmw-prod/7329c998-6dfd-4764-865a-1839dbcc5653.svg',
			title: trans('Atomic', '原子化'),
			txt: trans(
				'The functionality provided by each component is just right for the business needs.',
				'每个组件提供的功能，恰到好处地满足业务所需'
			)
		},
		{
			img: 'https://gw.alipayobjects.com/zos/bmw-prod/0c1d3f71-9b1a-43df-84a8-8eed55700d65.svg',
			title: trans('Fluent', '流畅'),
			txt: trans(
				'With smooth gesture interaction and detailed animation, it helps to create the ultimate experience.',
				'拥有流畅的手势交互和细致的动画展示，助力打造极致体验'
			)
		}
	];
	return (
		<div className={styles.homePage}>
			{/* 内容部分 */}
			<div className={styles.main}>
				<div className={styles.header}>
					<div className={styles.headerLeft}>
						<h1 className={styles.title}>EUI Desktop</h1>
						<p className={styles.description}>桌面端组件库</p>
						<p className={styles.buttons}>
							<a href={trans('#/guide/quick-start', '#/guide/quick-start')}>
								{trans('Get Started', '开始使用')}
							</a>
							<a href={trans('#/components', '#/components')}>{trans('Components', '组件列表')}</a>
						</p>
					</div>
					<img
						className={styles.headerImage}
						alt="header-image"
						src="https://gw.alipayobjects.com/mdn/rms_25513e/afts/img/A*72wxQ7yN4tEAAAAAAAAAAAAAARQnAQ"
					/>
				</div>
				{/* 功能特性 */}
				<div className={styles.group}>
					<p className={styles.groupTitle}>{trans('Features', '功能特性')}</p>
					<ul className={styles.features}>
						{characteristics.map((item) => {
							return (
								<li key={item.title}>
									<p>
										<img src={item.img} />
									</p>
									<p>{item.title}</p>
									<p>{item.txt}</p>
								</li>
							);
						})}
					</ul>
				</div>
			</div>
		</div>
	);
};
