.homePage {
  overflow: hidden;
  a {
    text-decoration: none;
  }
  .main {
    margin: 0 auto;
    max-width: 1200px;
    min-width: 960px;
    padding: 0 48px;
    box-sizing: content-box;
    .header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .headerLeft {
        position: relative;
        z-index: 1;
        .buttons {
          margin-top: 20px;
        }
        .title {
          font-size: 48px;
          font-weight: normal;
          color: rgba(0, 0, 0, 0.9);
          line-height: 1;
          margin: 0;

          [data-prefers-color='dark'] & {
            color: unset;
          }
        }
        .description {
          font-size: 20px;
          color: rgba(0, 0, 0, 0.85);
          line-height: 28px;
          margin-bottom: 56px;

          [data-prefers-color='dark'] & {
            color: unset;
          }
        }
        .buttons {
          margin-top: 28px;
          a {
            display: inline-block;
            font-size: 16px;
            padding: 10px 36px;
            border-radius: 100px;
            border: 1px solid #3978ff;
            background-color: #fff;
            color: #3978ff;
            margin-right: 24px;
          }
          a:first-child {
            background-color: #3978ff;
            color: #ffff;
          }
        }
      }
      .headerImage {
        width: 960px;
        transform: translateX(30%);
        margin-left: -40%;
        position: relative;
        z-index: 0;
        [data-prefers-color='dark'] & {
          visibility: hidden;
        }
        @media (max-width: 1300px) {
          & {
            width: 820px;
            transform: translateX(30%);
            margin-left: -30%;
          }
        }
      }
    }
    .group {
      padding: 32px 0 24px;
    }
    .groupTitle {
      font-size: 24px;
      color: #697a8c;
      line-height: 32px;
      margin-top: 12px;
      margin-bottom: 48px;
    }
    ul.features {
      display: flex;
      justify-content: space-between;
      li {
        list-style: none;
        p:nth-child(1) {
          margin: 0 auto;
          text-align: center;
        }
        p:nth-child(2) {
          height: 24px;
          width: 100%;
          font-size: 16px;
          color: rgba(0, 0, 0, 0.85);
          line-height: 24px;
          margin-top: 30px;
          margin-bottom: 8px;
          text-align: center;

          [data-prefers-color='dark'] & {
            color: unset;
          }
        }
        p:nth-child(3) {
          width: 210px;
          font-size: 14px;
          color: #697a8c;
          text-align: center;
          line-height: 24px;
          margin-top: 8px;
        }
      }
    }
    .resourcesCardList {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: stretch;
      gap: 48px;
      .resourcesCard {
        flex: 1;
        //width: 547px;
        background: #fafcfe;
        position: relative;
        padding-left: 32px;
        padding-right: 24px;
        padding-top: 32px;

        [data-prefers-color='dark'] & {
          background: #323233;
        }

        &:nth-child(2) {
          background-color: rgba(223, 246, 255, 0.3);

          [data-prefers-color='dark'] & {
            background-color: #323233;
          }
        }

        .development_con_title {
          height: 32px;
          line-height: 32px;
          display: flex;
          justify-content: space-between;
          span {
            font-size: 20px;
            color: rgba(0, 0, 0, 0.85);
            [data-prefers-color='dark'] & {
              color: unset;
            }
          }
          a {
            font-size: 14px;
            color: #3978ff;
          }
        }
        p:nth-child(2) {
          margin-top: 16px;
          height: 26px;
          font-size: 14px;
          color: #697a8c;
          line-height: 26px;
        }
        img {
          margin-top: 36px;
          display: block;
        }
        .development_pos {
          position: absolute;
          top: 0;
          right: 0;
          z-index: -1;
          height: 100%;
          width: 100%;
          background-image: linear-gradient(
            210deg,
            #f4f9ff 0%,
            rgba(230, 241, 255, 0) 45%
          );
        }
      }
    }
    .guidance {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: stretch;
      gap: 48px;
      .guidanceCard {
        flex: 1;
        background: #ffffff;
        box-shadow: 0 4px 10px 0 rgba(184, 212, 231, 0.2);
        display: flex;
        justify-content: space-between;
        padding: 32px;
        [data-prefers-color='dark'] & {
          background: #323233;
        }
        > div {
          width: 274px;
          p:nth-child(1) {
            height: 32px;
            font-size: 20px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 32px;
            margin-top: 0;
            margin-bottom: 8px;

            [data-prefers-color='dark'] & {
              color: unset;
            }
          }
          p:nth-child(2) {
            font-size: 14px;
            color: #697a8c;
            line-height: 26px;
            margin-top: 0;
            margin-bottom: 34px;
          }
          a {
            font-size: 14px;
          }
        }
        img {
          width: 200px;
          height: 132px;
        }
      }
    }
    ul.using {
      display: flex;
      justify-content: space-between;
      gap: 24px;
      margin: 0;
      padding: 0;
      li {
        list-style: none;
        flex: none;
      }
      img {
        height: 34px;
        filter: grayscale(100%);
      }
      li:nth-child(7) {
        img {
          height: 54.1px;
          margin-top: -10px;
        }
      }
    }
  }
  .footer {
    margin-top: 60px;
    background: #06080a;

    .columns {
      margin: 0 auto;
      width: 1140px;
      display: flex;
      justify-content: space-between;
      padding: 80px 0 32px;
      ul {
        flex: 1;
        padding: 0 36px 0 0;
        li {
          font-size: 14px;
          line-height: 22px;
          margin-bottom: 12px;
          list-style: none;
          a {
            color: rgba(255, 255, 255, 0.9);
          }
        }
        li:first-child {
          font-size: 16px;
          color: #ffffff;
          margin-bottom: 24px;
        }
      }
    }
    .copyright {
      padding: 12px 0;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.65);
      text-align: center;
      line-height: 22px;
    }
  }
}
