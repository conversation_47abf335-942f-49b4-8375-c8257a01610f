# FAQ

### 支持小程序吗？

eui 本身只支持 React 技术栈。

对于支付宝小程序，可以使用 [antd-mini](https://mini.ant.design/)。

微信和其他平台的小程序暂时还没有对应的孪生组件库，欢迎社区同学开发。


### 如何避免 300ms 的点击延迟？

在 `head` 中增加以下内容：

```html
<meta name="viewport" content="width=device-width">
```

或者增加以下全局样式：

```css
html {
  touch-action: manipulation;
}
```

具体请参考这两篇文章：

- [300ms tap delay, gone away](https://developers.google.com/web/updates/2013/12/300ms-tap-delay-gone-away)
- [More Responsive Tapping on iOS](https://webkit.org/blog/5610/more-responsive-tapping-on-ios/)

### 在我的项目中，eui 的组件手势操作无法生效，该怎么解决？

请检查项目中是不是引入了 fastclick，如果有的话，尝试移除掉再试一下。

### 关于 React Hot Loader

React Hot Loader 对项目有比较大的侵入性， 而 eui 中的很多组件（例如 Swiper Tabs Form TabBar SideBar Dropdown Space Steps）并不能和它兼容，而且 React Hot Loader 本身也在 README 中写了推荐大家不要再使用，所以请考虑移除 React Hot Loader 或将其替换为 [React Fast Refresh](https://github.com/facebook/react/issues/16604)。

