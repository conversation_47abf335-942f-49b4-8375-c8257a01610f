# 国际化

### ConfigProvider

eui 提供了 [ConfigProvider](../components/config-provider) 组件用于全局配置国际化文案。

```jsx
import zhCN from '@hose/eui/es/locale/zh_CN';

return (
  <ConfigProvider locale={zhCN}>
    <App />
  </ConfigProvider>
)
```

目前支持以下语言：
| 语言 | 文件名 |
|----------------------|--------|
| 简体中文 | zh-CN |
| 繁体中文（中国香港） | zh-HK |
| 繁体中文（中国台湾） | zh-TW |
| 英语（美式） | en-US |

具体的使用方法请参考 [ConfigProvider](../components/config-provider) 文档。

### 增加语言包

如果你找不到你需要的语言包，欢迎你在 [英文语言包](https://github.com/ant-design/ant-design-mobile/blob/master/src/locales/en-US.ts) 的基础上创建一个新的语言包，并给我们发一个 Pull Request。
[语言对照表](http://www.lingoes.net/en/translator/langcode.htm)

基本步骤如下：

1. 在 `src/locales` 中增加语言包。
2. 在 [config-provider.test.tsx](https://github.com/ant-design/ant-design-mobile/blob/master/src/components/config-provider/tests/config-provider.test.tsx) 添加改语言的测试用例。
3. 运行 `yarn test -u` 命令，更新 snapshots。
4. （可选）更新文档 [i18n.zh.md](https://github.com/ant-design/ant-design-mobile/blob/master/docs/guide/i18n.zh.md) 和 [i18n.en.md](https://github.com/ant-design/ant-design-mobile/blob/master/docs/guide/i18n.en.md)，增加语言列表。

