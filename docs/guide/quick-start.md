# 快速上手

## 安装

```bash
$ npm install --save @hose/eui
# or
$ yarn add @hose/eui
```

## 引入

### 1. 引入样式

#### 1.1 引用预设样式

```js
import '@hose/eui-theme/dist/eui-style.css';
```

#### 1.2 动态配置样式

```js
import EuiTheme from '@hose/eui-theme';
theme = new EuiTheme.ConfigTheme();
theme.config({ prefix: 'eui', platform: 'eui' });
```

platConfig 参数：

| 参数       | 说明                                                                                                                              |
| ---------- | --------------------------------------------------------------------------------------------------------------------------------- |
| platform   | 平台配置，作用域                                                                                                                  |
| prefix     | 变量前缀                                                                                                                          |
| parse      | 是否解析，例:解析的 sys:--eui-primary-pri-50: rgba(232, 241, 255, 1);,不解析的 sys:--eui-primary-pri-50: rgba(var(--eui-B50), 1); |
| brandColor | 品牌色，填写则覆盖默认的品牌色                                                                                                    |

### 2. 引入组件

引入组件时，eui-mobile 会自动为你加载 css 样式文件：

```js
import { Button } from '@hose/eui-mobile';
```

### 配置样式作用域

配置样式作用域，默认 eui-theme 的作用域为

```html
<html theme-platform="eui"></html>
```

注：`theme-platform` 可配置在任意标签下，只有在标签范围内 token 才生效

样式作用域的目的是防止和老页面里的样式冲突

## 兼容性

我们建议在项目中增加下面的 babel 配置，这样可以达到最大兼容性，为 iOS Safari `>= 10` 和 Chrome `>= 49`：

```json
{
	"presets": [
		[
			"@babel/preset-env",
			{
				"targets": {
					"chrome": "49",
					"ios": "10"
				}
			}
		]
	]
}
```

对于 TypeScript，我们兼容的版本是 `>= 3.8`。

对于 React，我们兼容的版本是 `^16.8.0` `^17.0.0` `^18.0.0`。
