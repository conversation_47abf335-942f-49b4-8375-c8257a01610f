# 什么是试验性

试验性特性是一些我们还处于探索阶段的特性，例如一些 API 和交互还不太确定的组件，一些可能在未来有所调整的技术方案，一些命名或者含义可能会在未来发生变动的组件属性。 我们会提前将其公开发布出来，并且会在文档上有一个**试剂瓶的图标进行标识**。各位开发者可以尝试使用，也欢迎反馈意见和建议～

**在未来的 minor 和 major 版本中，这些试验性的特性可能会功能发生变化，API 出现 break change，甚至会被完全移除掉**。但是不要担心，我们会在以下情况发生时，**明确地在发布日志中进行提示**：

- 当一个试验性特性出现 break change 时
- 当一个试验性特性已经达到成熟标准，从试验性特性变为常规特性时

**所以如果你使用了这些试验性特性，我们推荐：**

- 订阅和关注我们的[发布日志](https://github.com/ant-design/ant-design-mobile/releases)（强烈推荐）
- 使用 lockfile 锁定版本（推荐）
- 手动设置 eui 依赖的版本范围为 `~` （推荐）

当然，不管是试验性特性还是常规特性，我们都会对它们保持一贯严格的质量标准。
