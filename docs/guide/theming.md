# 主题

得益于 CSS 变量强大而灵活的能力， eui 基于 eui-theme 生成的一套 Design Token 样式变量。

## EUI Theme 支持功能

- 统一颜色、文字、尺寸为 token 变量，各业务方只需要根据 figma 设计稿上的备注，引用对应变量，即可实现效果样式，不用再关系具体值
- 主题色可动态配置，可适时切换主题
- 多种主题可共存。如：费控、商城作为业务使用，可能会同时存在一个页面，而产品要求风格不同
- 支持 亮/黑 模式，可实时切换，可嵌套
- 全局样式更新维护机制，达到各业务方样式统一。目的是做一套通用的设计系统，费控、商城都将是业务方

## 生成主题

```shell
npm install -g @hose/eui-theme

eui-theme -p eui --color '#22B2CC' --parse --out ./src/global/eui-style.less
```

```shell
# eui-theme 命令用法

Options:
  -p, --platform <platform>  配置平台
  --prefix <prefix>          配置前缀
  --color <color>            配置颜色
  --out <file>               生成文件路径
  --parse                    直接生成无 ref 变量的样式
  --setting <settingFile>    设置文件，设置主题的基础数据（颜色、字体、尺寸等）
```

## 主题切换

配置 `theme-platform="eui"` 主题作用域，开启对应主题，`theme-mode` 配置要使用的模式（light\dark）,默认 `light`。

```jsx
import React from 'react';
import { Button } from '@hose/eui';
import './theming.less';

export default () => {
  return (
    <div className="dark-content" theme-platform="eui">
      <div>普通模式</div>
      <div className="content" theme-mode="dark">
        <div>深色模式</div>
        <div className="content" theme-mode="light">
          <div>亮色模式</div>
          <div className="content" theme-mode="dark">
            深色模式
            <div>
              <Button color="primary" size="small">
                按钮
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```

```jsx | preview
/**
 * inline: true
 */

import React from 'react';
import { Button } from '@hose/eui';
import './theming.less';

export default () => {
  return (
    <div className="dark-content" theme-platform="eui">
      <div>普通模式</div>
      <div className="content" theme-mode="dark">
        <div>深色模式</div>
        <div className="content" theme-mode="light">
          <div>亮色模式</div>
          <div className="content" theme-mode="dark">
            深色模式
            <div>
              <Button color="primary" size="small">
                按钮
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```
