export default {
	basic: [
		'/components/button',
		'/components/icon',
		'/components/space',
		'/components/divider'
	],
	dataDisplay: [
		'/components/tooltip',
		'/components/tour',
		'/components/avatar',
		'/components/badge',
		'/components/card',
		'/components/image',
		// '/components/image-viewer',
		// '/components/infinite-scroll',
		// '/components/notice-bar',
		// '/components/page-indicator',
		'/components/popover',
		// '/components/swiper',
		'/components/tag',
		'/components/list',
		'/components/table',
		// '/components/water-mark',
		'/components/collapse',
		'/components/timeline',
		'/components/carousel',
		'/components/description',
		'/components/segmented',
		'/components/ellipsis'
	],
	dataEntry: [
		'/components/cascader',
		// '/components/cascader-view',
		// '/components/check-list',
		'/components/checkbox',
		'/components/color-picker',
		'/components/form',
		'/components/input',
		'/components/input-number',
		'/components/select',
		'/components/transfer',
		// '/components/picker',
		// '/components/picker-view',
		'/components/radio',
		'/components/rate',
		// '/components/search-bar',
		// '/components/selector',
		'/components/slider',
		// '/components/stepper',
		'/components/switch',
		// '/components/text-area',
		'/components/date-picker',
		'/components/time-picker',
		'/components/tree',
		'/components/tree-select',
		'/components/upload'
	],
	feedback: [
		// '/components/action-sheet',
		// '/components/dialog',
		'/components/alert',
		'/components/message',
		'/components/notification',
		'/components/error-block',
		'/components/spin',
		// '/components/loading',
		// '/components/mask',
		'/components/drawer',
		'/components/modal',
		'/components/progress',
		// '/components/pull-to-refresh',
		// '/components/result',
		'/components/skeleton',
		'/components/popconfirm',
		// '/components/swipe-action',
		// '/components/toast',
		'/components/attachment',
	],
	navigationAndLayout: [
		'/components/menu',
		'/components/layout',
		'/components/breadcrumb',
		'/components/dropdown',
		'/components/pagination',
		// '/components/auto-center',
		// '/components/capsule-tabs',
		// '/components/floating-panel',
		// '/components/grid',
		// '/components/index-bar',
		// '/components/jumbo-tabs',
		// '/components/nav-bar',
		// '/components/popup',
		// '/components/safe-area',
		// '/components/side-bar',
		// '/components/tab-bar',
		'/components/tabs',
		'/components/steps',
		'/components/page-header'
	],
	other: ['/components/config-provider', '/components/anchor'],
	business: ['/components/questionnaire'],
	experimental: [
		// '/guide/what-is-experimental',
		// '/components/calendar',
		// '/components/ellipsis',
		// '/components/floating-bubble',
		// '/components/image-uploader',
		// '/components/number-keyboard',
		// '/components/passcode-input',
		// '/components/tree-select',
		// '/components/virtual-input',
		// '/components/scroll-mask',
	]
};
