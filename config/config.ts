import components from './components';
import { IConfig } from 'dumi';
import pkg from '../package.json';

const isProduction = process.env.NODE_ENV === 'production';

const config: IConfig = {
	mode: 'site',
	publicPath: isProduction ? `https://statics.ekuaibao.com/eui/eui-web/${pkg.version}/` : '/',
	title: 'EUI.Design',
	logo: 'https://static.ekuaibao.com/tmc/eui/eui.svg',
	favicon: 'https://static.ekuaibao.com/tmc/eui/eui.svg',
	navs: [
		{
			title: '指南',
			path: '/guide'
		},
		{
			title: '组件',
			path: '/components'
		},
		{
			title: '移动端组件',
			path: 'https://eui-mobile.serverless.ekuaibao.com'
		},
		{
			title: '高级组件',
			path: 'https://static.ekuaibao.com/eui/eui-pro/0.1.27/index.html'
		},
		{
			title: 'Git',
			path: 'https://git.ekuaibao.com/fe-core/eui'
		}
	],
	menus: {
		'/': [
			{
				title: '首页',
				path: 'index'
			}
		],
		'/guide': [
			{
				title: '快速上手',
				path: '/guide/quick-start'
			},
			{
				title: '常见问题',
				path: '/guide/faq'
			},
			{
				title: '主题',
				path: '/guide/theming'
			},
			{
				title: '按需加载',
				path: '/guide/import-on-demand'
			},
			{
				title: '国际化',
				path: '/guide/i18n'
			},
			{
				title: '关于试验性',
				path: '/guide/what-is-experimental'
			},
			// {
			//   title: '高清适配（试验性）',
			//   path: '/guide/hd',
			// },
			{
				title: '服务端渲染 / SSR（试验性）',
				path: '/guide/ssr'
			}
		],
		'/components': [
			{
				title: '基础',
				children: components.basic
			},
			{
				title: '数据展示',
				children: components.dataDisplay
			},
			{
				title: '数据录入',
				children: components.dataEntry
			},
			{
				title: '反馈',
				children: components.feedback
			},
			{
				title: '导航和布局',
				children: components.navigationAndLayout
			},
			{
				title: '其他',
				children: components.other
			},
			{
				title: '业务',
				children: components.business
			},
			{
				title: '试验性',
				children: components.experimental
			}
		]
	},
	resolve: {
		includes: ['docs', 'src'],
		passivePreview: true
	},
	alias: {
		'@hose/eui/es': process.cwd() + '/src',
		demos: process.cwd() + '/src/demos/index.ts'
	},
	metas: [
		{
			name: 'viewport',
			content:
				'width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover'
		}
	],
	hash: true,
	mfsu: {},
	history: {
		type: 'hash'
	},
	dynamicImport: {
		loading: '@/demos/Loading',
	},
	locales: [
		['zh', '中文']
		// ['en', 'English'],
	],
	styles: [
		`
    html {
      touch-action: manipulation;
    }
    #root .__dumi-default-mobile-demo-layout {
      padding: 0;
    }
    html {
      min-height: 100vh;
    }
    `
	]
	// themeConfig: {
	//   hd: {
	//     rules: [
	//       // {mode: 'vw', options: [100, 750]}
	//     ],
	//   },
	// },
};

export default config;
