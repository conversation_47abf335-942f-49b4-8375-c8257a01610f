import { spawnSync } from 'child_process';
import { existsSync, mkdirSync, rmSync, writeFileSync, readdirSync, statSync, readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

/** 当前目录 */
const currentPath = path.dirname(fileURLToPath(import.meta.url));
/** 获取当前文件目录 */
const tempPath = path.join(currentPath, './temp');

// 布丁版本号，每次发布 +1
const patchVersion = 3;

/** windows环境判断 */
const isWin = process.platform === 'win32';

/** 跨平台 npm pnpm 执行命令 */
const npm = isWin ? 'npm.cmd' : 'npm';
const pnpm = isWin ? 'pnpm.cmd' : 'pnpm';

/** rc npm 包列表 */
const rcNpmList = {
	'rc-align': '4.0.15',
	'rc-cascader': '3.7.0',
	'rc-checkbox': '2.3.2',
	'rc-collapse': '3.4.2',
	'rc-dialog': '9.0.2',
	'rc-drawer': '6.1.0',
	'rc-dropdown': '4.0.0',
	'rc-field-form': '1.27.0',
	'rc-image': '5.13.0',
	'rc-input': '0.1.4',
	'rc-input-number': '7.3.9',
	'rc-mentions': '1.13.1',
	'rc-menu': '9.8.1',
	'rc-motion': '2.9.0',
	'rc-notification': '4.6.0',
	'rc-overflow': '1.3.0',
	'rc-pagination': '3.2.0',
	'rc-picker': '2.7.2',
	'rc-progress': '3.4.1',
	'rc-rate': '2.9.0',
	'rc-resize-observer': '1.2.0',
	'rc-segmented': '2.1.0',
	'rc-select': '14.9.2',
	'rc-slider': '10.0.0',
	'rc-steps': '6.0.0',
	'rc-switch': '3.2.0',
	'rc-table': '7.26.0',
	'rc-tabs': '12.5.10',
	'rc-textarea': '0.4.5',
	'rc-tooltip': '5.2.0',
	'rc-tree': '5.7.0',
	'rc-tree-select': '5.13.0',
	'rc-trigger': '5.3.4',
	'rc-upload': '4.3.0',
	'rc-util': '5.30.0',
	'rc-virtual-list': '3.2.0'
};

const rcNpmNameList = Object.keys(rcNpmList);

/**
 * 获取目录下所有子目录的 js 文件
 */
function getJsTsFiles(dir) {
	// 存储所有 JS/TS 文件
	let results = [];
	// 读取当前目录下的所有文件和目录
	const files = readdirSync(dir);
	// 遍历每个文件或目录
	for (let file of files) {
		// 获取文件或目录的完整路径
		const filePath = path.join(dir, file);
		// 判断该路径是否为目录
		const stats = statSync(filePath);
		if (stats.isDirectory()) {
			// 如果是目录，则递归调用该函数来扫描子目录
			results = results.concat(getJsTsFiles(filePath));
		} else {
			// 如果是 JavaScript 或 TypeScript 文件，则将其添加到结果数组中
			if (path.extname(file) === '.js' || path.extname(file) === '.ts') {
				results.push(filePath);
			}
		}
	}
	return results;
}

/**
 * 遍历包文件，并替换文件内容
 */
function traverseFile(npmName, callback) {
	const curPath = path.join(tempPath, npmName);
	const files = getJsTsFiles(curPath);
	files.forEach((file) => {
		const fileText = readFileSync(file, 'utf-8').replace(new RegExp(`(declare module |from |require\\()(["'])(${rcNpmNameList.join('|')})`, 'g'), `$1$2@hose/eui-$3`);
		writeFileSync(file, fileText);
	});
}

/** 发布 npm 包到私有仓库 */
const publishNpm = (npmName, npmVersion) => {
	const curPath = path.join(tempPath, npmName);
	console.log(`⏳ ${npmName} 包发布中...`);
	spawnSync(npm, ['publish', '--registry=https://npm.ekuaibao.com'], { cwd: curPath });
	console.log(`✅ ${npmName} 发布完成`);
};

/** 修改 package.json 名称和依赖 */
const changePackageInfo = async (npmName) => {
	const packageJsonPath = path.join(tempPath, npmName, 'package.json');
	const pkg = JSON.parse(readFileSync(packageJsonPath));
	// 修改包名称
	pkg.name = `@hose/eui-${pkg.name}`;
	pkg.version = `${pkg.version}-patch.${patchVersion}`;
	// 删除发布命令
	delete pkg.scripts.prepublishOnly;
	delete pkg.scripts.prepare;
	delete pkg.scripts.postpublish;
	// 修改依赖的 rc-xx 的包名称
	Object.keys(pkg.dependencies).forEach((name) => {
		if (rcNpmNameList.includes(name)) {
			pkg.dependencies[`@hose/eui-${name}`] = `${rcNpmList[name]}-patch.${patchVersion}`;
			delete pkg.dependencies[name];
		}
	});
	// 重新生成 package.json
	const packageJson = JSON.stringify(pkg, null, 2);
	writeFileSync(packageJsonPath, packageJson);
	traverseFile(npmName);
	publishNpm(npmName, pkg.version);
};

/** 处理 npm 包 */
const handleNpm = async () => {
	console.log('🚚 复制&处理 npm 包...');
	for (const name of Object.keys(rcNpmList)) {
		const _npmPath = path.join(tempPath, 'node_modules', name);
		const _tempPath = path.join(tempPath, name);
		mkdirSync(_tempPath, { recursive: true });
		isWin
			? spawnSync('xcopy', [_npmPath, _tempPath, '/E', '/Y'])
			: spawnSync('cp', ['-R', `${_npmPath}/`, `${_tempPath}/`]);
		await changePackageInfo(name);
	}
	console.log('🎉 全部完成，请替换 package.json 中的 rc-xx 包为 @hose/eui-rc-xx');
};

/** 下载 npm 包 */
const downloadNpm = () => {
	if (existsSync(tempPath)) {
		console.log('🧹 清理临时文件');
		rmSync(tempPath, { recursive: true, force: true });
	}
	mkdirSync(tempPath);
	const npmNameList = Object.entries(rcNpmList).map(([key, version]) => `${key}@${version}`);
	console.log('🚚 npm 包安装中...');
	spawnSync(pnpm, ['init'], { cwd: tempPath });
	spawnSync(pnpm, ['add', ...npmNameList], { cwd: tempPath, stdio: 'inherit' });
	console.log('✅ npm 安装完成');
};

downloadNpm();
handleNpm();
